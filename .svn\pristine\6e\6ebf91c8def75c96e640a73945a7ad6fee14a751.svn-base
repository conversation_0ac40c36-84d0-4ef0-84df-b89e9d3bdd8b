<template>
  <view>
    <view class="orderList">
      <view class="orderItem" v-for="item in page.orderList" :key="item.id">
        <view class="orderItem_title">
          <!-- 显示该用户待接单的需求和待出货、待收货的订单 -->
          <!-- 需求单： 0 待分配,1 待接单,2 已接单,3 已取消 -->
          <!-- 订单：   0 待出货,1 待收货,2 已完成,3 已取消,4 待接单 -->
          <view class="title_status" :class="[
            item.status == 4
              ? 'status1'
              : item.status == 1
                ? 'status0'
                : item.status == 0
                  ? 'status2'
                  : '',
          ]">
            {{ getStatusName(item.status, item.orderId) }}
          </view>
          <view class="title_name">{{ item.category }}</view>
          <view class="title_id">{{ item.code }}</view>
        </view>
        <view class="orderItem_content">
          <view class="content_info">
            <view class="content_item">
              <view class="title" v-if="!item.orderId && item.status == 1">最新议价(元/斤)</view>
              <view class="title" v-else>成交单价(元/斤)</view>
              <view class="price">{{ item.lastPrice || "--" }}</view>
            </view>
            <view class="content_item">
              <view class="title">采购报价(元/斤)</view>
              {{ item.originalPrice || "--" }}
            </view>
            <view class="content_item">
              <view class="title">收鱼日期</view>
              {{ item.acceptTime }}
            </view>
          </view>
          <view class="content_remind" v-if="item.msg">
            <image class="remindIcon" :src="config.imageUrl + '/shop/remind.png'"></image>
            {{ item.msg }}
          </view>
          <view class="content_btn">
            <!-- 显示该用户待接单的需求和待出货、待收货的订单 -->
            <!-- 需求单： 0 待分配,1 待接单,2 已接单,3 已取消 -->
            <!-- 订单：   0 待出货,1 待收货,2 已完成,3 已取消,4 待接单 -->

            <!-- 待接单 -->
            <template v-if="!item.orderId && item.status == 1">
              <view class="btnItem" @click="gotoDemandDetail(item)">查看需求</view>
              <view class="btnItem" @click="gotoBargain(item)">议价</view>
              <view class="btnItem lightBtn" v-if="item.msgFlag" @click="showEnsurePopup(item)">确认接单</view>
            </template>
            <!-- 待出货 -->
            <template v-else-if="item.orderId && item.status == 0">
              <view class="btnItem" @click="gotoOrderDetail(item)">查看订单</view>
              <view class="btnItem lightBtn" @click="ensureShipment(item)">确认出货</view>
            </template>
            <!-- 待收货 -->
            <template v-else-if="item.orderId && item.status == 1">
              <view class="btnItem" @click="gotoOrderDetail(item)">查看订单</view>
              <view class="btnItem lightBtn" @click="shipmentInfo(item)">出货信息</view>
            </template>
          </view>
        </view>
      </view>
      <view style="padding: 20rpx 0" v-if="noData === false">
        <up-loadmore :status="loadStatus" />
      </view>
      <view class="orderItem" style="padding: 32rpx 0" v-if="noData === true">
        <view class="noDataBox">
          <image :src="config.imageUrl + '/shop/demandNull.png'"></image>
          <text>暂无数据</text>
        </view>
      </view>
    </view>
  </view>
  <up-popup v-model:show="showEnsure" mode="center" :round="'20rpx'">
    <view class="agreeView">
      <view class="content">
        是否确认以<view class="redColor">{{ agreePrice }}元/斤</view>价格接单？
      </view>
      <view class="agreeBtn">
        <button @click="cancelClick">取消</button>
        <button @click="confirmClick">确认</button>
      </view>
    </view>
  </up-popup>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import { onLoad, onShow, onReachBottom, onUnload, onHide } from "@dcloudio/uni-app";
// #ifndef MP-WEIXIN
import { parseWithBigInt } from "@/http/webSocket.js";
// #endif
import eventBus from "@/http/eventBus.js";

// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";
const config = proxy.ipConfig;

const page = reactive({
  orderList: [],
});
let noData = ref(false);
let loadStatus = ref("loading"); // 加载更多状态

const pager = reactive({
  page: 1, // 当前页码
  size: 10, // 每页数量
  total: 0, // 总数据量
});

onShow(() => {
  pager.page = 1
  page.orderList = [];
  getPendingList();
  eventBus.$on("haveNewMsg", () => {
    pager.page = 1
    page.orderList = []
    getPendingList()
  });
});

onHide(() => {
  eventBus.$off("haveNewMsg");
})
onUnload(() => {
  eventBus.$off("haveNewMsg");
})

onReachBottom(() => {
  let loadTotal = pager.page * pager.size;
  if (loadTotal < pager.total) {
    // 判断是否还有更多数据
    loadStatus.value = "loading"; // 设置加载状态
    pager.page++; // 页码加1
    setTimeout(() => {
      getPendingList('add'); // 调用获取数据的方法
    }, 300);
  }
});

// !以前的socket监听事件
// // #ifdef MP-WEIXIN
// // #endif
// // #ifndef MP-WEIXIN
// if (proxy.socket) proxy.socket.addEventListener('message', webSocketOnMessage)
// onUnload(() => {
//   if (proxy.socket) proxy.socket.removeEventListener('message', webSocketOnMessage)
// })

// function webSocketOnMessage(event) {
//   let data = parseWithBigInt(event.data);
//   pager.page = 1
//   page.orderList = [];
//   getPendingList()
// }
// // #endif

function getPendingList(isAdd) {
  let postObj = {
    page: pager.page,
    size: pager.size,
  };
  http
    .post(proxy.ipConfig.baseUrl2 + "/farmers/page", postObj, { method: "GET" })
    .then((res) => {
      if (res.data.code == 2000) {
        if (isAdd) {
          page.orderList.push(...res.data.data.records);
        } else {
          page.orderList = res.data.data.records;
        }
        pager.total = res.data.data.total;
        if (pager.page * pager.size >= pager.total && pager.total > 0) {
          loadStatus.value = "nomore";
        }
        if (page.orderList.length < 1) {
          noData.value = true;
        } else {
          noData.value = false;
        }
      } else if (res.data.code == 4001) {
        loadStatus.value = "nomore";
        uni.showToast({
          title: "请登录后操作",
          icon: "none",
          mask: true,
          duration: 2000,
          success: () => {
            setTimeout(() => {
              uni.redirectTo({
                url: "/pages/loginView/index",
              });
            }, 2000);
          },
        });
      } else {
        loadStatus.value = "nomore";
        uni.showToast({
          title: res.data.message,
          icon: "none",
        });
      }
    });
}

function getStatusName(status, orderId) {
  // 待接单的需求  和  待出货、待收货的订单
  // 需求单： 0 待分配,1 待接单,2 已接单,3 已取消
  // 订单：   0 待出货,1 待收货,2 已完成,3 已取消,4 待接单
  status = status + "" + (orderId > 0 ? 2 : 1);
  switch (status) {
    case "11":
      return "待接单";
    case "02":
      return "待出货";
    case "12":
      return "待收货";
    default:
      return "";
  }
}

// 议价
const gotoBargain = async (item) => {
  let status = await checkDemandStatus(item.dpId)
  if (!status) {
    uni.showToast({
      title: "该需求已取消！",
      icon: "none",
      mask: true,
      duration: 2000,
    })
    setTimeout(() => {
      pager.page = 1
      page.orderList = []
      getPendingList()
    }, 2000);
    return
  }
  readMsg(item.demandId)
  console.log("item", item.demandId, item.dpId);
  uni.navigateTo({ url: `/pagesPackages6/demandShView/bargain?demandId=${item.demandId}&dpId=${item.dpId}` })
  return
  // #ifdef MP-WEIXIN
  uni.navigateTo({ url: `/pagesPackages6/demandShView/bargainWx?demandId=${item.demandId}&dpId=${item.dpId}` })
  // #endif
  // #ifndef MP-WEIXIN
  uni.navigateTo({ url: `/pagesPackages6/demandShView/bargainWeb?demandId=${item.demandId}&dpId=${item.dpId}` })
  // #endif
}
// 需求详情
const gotoDemandDetail = async (item) => {
  let status = await checkDemandStatus(item.dpId)
  if (!status) {
    uni.showToast({
      title: "该需求已取消！",
      icon: "none",
      mask: true,
      duration: 2000,
    })
    setTimeout(() => {
      pager.page = 1
      page.orderList = []
      getPendingList()
    }, 2000);
    return
  }
  readMsg(item.demandId)
  uni.navigateTo({ url: "/pagesPackages6/demandShView/detail?demandId=" + item.demandId });
}
// 订单详情
function gotoOrderDetail(item) {
  uni.navigateTo({
    url: "/pagesPackages3/fishOrderView/detail?orderId=" + item.orderId,
  });
}
// 读取消息（只有需求单要读）
function readMsg(xqId) {
  let postObj = {
    xqId,
  }
  http.post(config.baseUrl2 + "/shsy/cleanMsg", postObj, { method: "POST" })
}

// 校验需求单是否被取消了
function checkDemandStatus(dpId) {
  return new Promise((resolve, reject) => {
    http.get(config.baseUrl2 + "/discussPrice/cancel", { params: { dpId } }, { method: "GET" })
      .then((res) => {
        if (res.data.code == 2000) {
          resolve(true)
        } else {
          resolve(false)
        }
      })
  })
}

// 确认接单
let showEnsure = ref(false);
let agreePrice = ref(0);
let dpId = ref("")
let demandId = ref("")
let orderId = ref("")
const showEnsurePopup = async (item) => {
  let status = await checkDemandStatus(item.dpId)
  if (!status) {
    uni.showToast({
      title: "该需求已取消！",
      icon: "none",
      mask: true,
      duration: 2000,
    })
    setTimeout(() => {
      pager.page = 1
      page.orderList = []
      getPendingList()
    }, 2000);
    return
  }
  dpId.value = item.dpId
  demandId.value = item.demandId
  orderId.value = item.orderId
  agreePrice.value = item.lastPrice || item.originalPrice
  showEnsure.value = true;
}

// 确认出货
function ensureShipment(item) {
  http.post(proxy.ipConfig.baseUrl2 + "/grzx/qtfw/getDefaultAddr", {}, { method: "GET", })
    .then((res) => {
      let data = res.data.data;
      if (data && data.uaddressId && data.defaultStatus == 1) {
        uni.navigateTo({
          url: `/pagesPackages3/fishOrderView/confirmShipment?orderId=${item.orderId}&pageType=6&dpId=${item.dpId}&demandId=${item.demandId}`,
        });
      } else {
        uni.showModal({
          title: "添加地址",
          content: "请添加起渔地址,并设置成默认地址!",
          confirmText: "添加",
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: "/pagesPackages2/receiveAddressView/index",
              });
            }
          },
        });
      }
    });
}
// 出货信息
function shipmentInfo(item) {
  uni.navigateTo({ url: "/pagesPackages3/fishOrderView/shipmentInformation?orderId=" + item.orderId, });
}

function cancelClick() {
  showEnsure.value = false;
}
function confirmClick() {
  showEnsure.value = false;
  let postObj = {
    dpId: dpId.value,
    demandId: demandId.value,
    orderId: orderId.value,
    token: uni.getStorageSync("user")?.token,
    senderType: uni.getStorageSync("user")?.yhlx,
    status: 1,
    type: 4,
    price: agreePrice.value,
    msg: ""
  };
  http.post(proxy.ipConfig.baseUrl2 + "/discussPrice/sendMessage", postObj, { method: "POST" })
    .then((res) => {
      if (res.data.code == 2000) {
        pager.page = 1
        page.orderList = []
        getPendingList()
      } else {
        uni.showToast({
          title: res.data.message,
          icon: "none",
        });
      }
    })
}
</script>

<style lang="scss" scoped>
.noDataBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666666;
  font-size: 24rpx;

  image {
    width: 400rpx;
    height: 400rpx;
    margin-bottom: 24rpx;
  }
}

.orderList {
  padding: 24rpx;
  background-color: #f5f5f5;

  .orderItem {
    padding: 0 24rpx;
    background-color: #fff;
    margin-bottom: 24rpx;
    border-radius: 20rpx;

    .orderItem_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      font-family: PingFang SC;
      font-size: 24rpx;
      font-weight: normal;

      .title_id {
        color: #666666;
      }

      .title_name {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
        margin: 0 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .title_status {
        height: 50rpx;
        line-height: 50rpx;
        border-radius: 20rpx 0;
        padding: 0 10rpx;
      }

      .status0 {
        color: #ff8d02;
        background: rgba(255, 141, 2, 0.1);
      }

      .status1 {
        color: #267fff;
        background: rgba(38, 127, 255, 0.1);
      }

      .status2 {
        color: #fa5151;
        background: rgba(250, 81, 81, 0.1);
      }

      .status3 {
        color: #ff8d02;
        background: rgba(255, 141, 2, 0.1);
      }
    }

    .orderItem_content {
      padding-bottom: 20rpx;
      padding: 10rpx 0 14rpx;

      .content_info {
        display: flex;
        border-radius: 20rpx;
        border: 2rpx solid #eaeaea;
        padding: 24rpx 20rpx;
      }

      .content_item {
        flex: 4;
        font-size: 28rpx;
        color: #333333;
        border-right: 2rpx solid #eaeaea;
        text-align: center;
        padding: 0 15rpx;

        &:first-child {
          padding-left: 0;
        }

        &:last-child {
          flex: 3.2;
          border-right: unset;
          // width: 150rpx;
          padding-right: 0;
        }

        .title {
          color: #666666;
          margin-bottom: 16rpx;
          font-size: 24rpx;
        }

        .price {
          color: #ff0000;
        }
      }

      .content_remind {
        display: flex;
        align-items: center;
        padding: 12rpx 8rpx;
        border-radius: 8rpx;
        gap: 10px;
        background: #f6f6f6;
        font-family: PingFang SC;
        font-size: 24rpx;
        color: #ff0000;
        margin-top: 24rpx;

        .remindIcon {
          width: 28rpx;
          height: 28rpx;
        }
      }

      .content_btn {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 24rpx;
        gap: 14rpx;

        .btnItem {
          width: 148rpx;
          border-radius: 10rpx;
          padding: 10rpx 0;
          background: #f4f4f4;
          text-align: center;
          font-family: PingFang SC;
          font-size: 24rpx;
          color: #333333;
        }

        .lightBtn {
          background: #effff3;
          color: #068324;
        }
      }
    }
  }
}

.agreeView {
  width: 600rpx;
  padding: 68rpx 50rpx 40rpx;
  box-sizing: border-box;

  .content {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    font-variation-settings: "opsz" auto;
    color: #333333;
    text-align: center;

    .redColor {
      color: #FF0000;
      display: inline;
      font-size: 36rpx;
    }
  }

  .agreeBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 40rpx;
    margin-top: 50rpx;

    button {
      width: 208rpx;
      height: 72rpx;
      line-height: 72rpx;
      border-radius: 72rpx;
      background: #F7F7F7;
      font-family: PingFang SC;
      font-size: 32rpx;
      color: #333333;
      margin: 0;

      &:nth-child(2) {
        background: #068324;
        color: #FFFFFF;
      }

      &:after {
        border: unset;
      }
    }
  }
}
</style>
