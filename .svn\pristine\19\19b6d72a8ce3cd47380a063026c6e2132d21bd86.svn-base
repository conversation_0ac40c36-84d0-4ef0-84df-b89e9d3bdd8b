<template>
  <view class="detailPage">
    <view class="topBg">
      <view class="msgBox">
        {{ orderDetailObj.orderCode }}
        <!-- 需求单状态 0：待分配，1：待接单，2：已接单，3：已取消
         订单状态0：待出货，1：待收货，2：已完成，3：已取消 4:待接单
        查的什么列表参考什么状态的注释 -->
        <view class="status" v-if="orderDetailObj.status == 0">待出货</view>
        <view class="status" v-if="orderDetailObj.status == 1">待收货</view>
        <view class="status" v-if="orderDetailObj.status == 2">已完成</view>
        <view class="status" v-if="orderDetailObj.status == 3">已取消</view>
        <view class="status" v-if="orderDetailObj.status == 4">待接单</view>
      </view>
      <image
        class="topImg"
        :src="config.imageUrl + '/myPage/fishDetail.svg'"
      ></image>
    </view>
    <!-- 养殖户联系方式 -->
    <view class="MerchantcontactInformation">
      <view class="lxfs">养殖户联系方式 </view>
      <view class="dhhm">
        {{ orderDetailObj.producerName }} {{ orderDetailObj.producerPhone }}
        <image
          class="dhhmicon"
          :src="config.imageUrl + '/myPage/tel.svg'"
          @click="getPlainText(orderDetailObj.producerPhoneEnc)"
        ></image>
      </view>
      <view class="address">
        {{ orderDetailObj.producerAddress }}
        <image
          class="dhhmicon"
          @click="copy(orderDetailObj.producerAddress)"
          :src="config.imageUrl + '/myPage/copy.svg'"
        ></image>
      </view>
    </view>
    <!-- 商品信息 -->
    <view class="orderGoods">
      <view class="orderGoodstitle">商品信息 </view>
      <view class="orderGoodsContent">
        <view class="orderGoodsItem">
          <view class="left">
            <view class="label"> 成交单价：</view>
            <view class="value valuered">
              {{ orderDetailObj.lastPrice }}元/斤</view
            >
          </view>
          <view class="right">
            <view class="label"> 采购报价：</view>
            <view class="value"> {{ orderDetailObj.originalPrice }}元/斤</view>
          </view>
        </view>
        <view class="orderGoodsItem">
          <view class="left">
            <view class="label"> 商品品类：</view>
            <view class="value">{{ orderDetailObj.category }}</view>
          </view>
          <view class="right">
            <view class="label">收鱼日期：</view>
            <view class="value">{{ orderDetailObj.acceptTime }}</view>
          </view>
        </view>
        <view class="orderGoodsItem" v-if="chDetailObj.amount">
          <view class="label"> 商品出货量：</view>
          <view class="value">{{ chDetailObj.amount }}斤 </view></view
        >
        <view class="orderGoodsItem" v-if="chDetailObj.totalPrice">
          <view class="label">商品成总交价： </view>
          <view class="value">{{ chDetailObj.totalPrice }}元 </view>
        </view>
      </view>
    </view>
    <!-- 订单信息 -->
    <view class="orderInfo">
      <view class="orderInfotitle">订单信息</view>
      <view class="orderInfocontent">
        <view class="orderInfoitem">
          <view class="orderInfoitemlabel">订单编号 </view>
          <view class="orderInfoitemvalue"
            >{{ orderDetailObj.orderCode }}
            <image
              @click="copy(orderDetailObj.orderCode)"
              class="copyImage"
              :src="config.imageUrl + '/orderDetail/copy.svg'"
            >
            </image
          ></view>
        </view>
        <view class="orderInfoitem">
          <view class="orderInfoitemlabel">分配时间 </view>
          <view class="orderInfoitemvalue">{{ orderDetailObj.sendTime }} </view>
        </view>
        <view class="orderInfoitem">
          <view class="orderInfoitemlabel">接单时间 </view>
          <view class="orderInfoitemvalue"
            >{{ orderDetailObj.orderTime }}
          </view>
        </view>
        <!-- 养殖户 -->
        <view
          class="orderInfoitem"
          v-if="orderDetailObj.status == 1 || orderDetailObj.status == 2"
        >
          <view class="orderInfoitemlabel">出货时间 </view>
          <view class="orderInfoitemvalue">{{ chDetailObj.sendTime }} </view>
        </view>
        <!-- 商户 -->
        <view class="orderInfoitem" v-if="orderDetailObj.finishTime">
          <view class="orderInfoitemlabel">收货时间 </view>
          <view class="orderInfoitemvalue"
            >{{ orderDetailObj.finishTime }}
          </view>
        </view>
        <view class="orderInfoitem" v-if="orderDetailObj.checkoutTime">
          <view class="orderInfoitemlabel">结算时间 </view>
          <view class="orderInfoitemvalue"
            >{{ orderDetailObj.checkoutTime }}
          </view>
        </view>
      </view>
    </view>
    <!-- 取消信息-->
    <view class="orderInfo" v-if="orderDetailObj.status == 3">
      <view class="orderInfotitle">取消信息</view>
      <view class="orderInfocontent">
        <view class="orderInfoitem">
          <view class="orderInfoitemlabel">取消时间 </view>
          <view class="orderInfoitemvalue">{{
            orderDetailObj.cancelTime
          }}</view>
        </view>
        <view class="orderInfoitem">
          <view class="orderInfoitemlabel">取消原因 </view>
          <view class="orderInfoitemvalue orderInfoitemvalue2">
            {{ orderDetailObj.cancelReason }}
          </view>
        </view>
      </view>
    </view>
    <!-- 底部操作按钮 -->
    <view class="operationButtons">
      <view class="operationButtonitem" @click="gotoXqsq(orderDetailObj)">
        需求详情</view
      >
      <view class="operationButtonitem" @click="gotoYjjl(orderDetailObj)">
        议价记录</view
      >
      <view
        v-if="orderDetailObj.status == 1 && orderDetailObj.status == 2"
        @click="gotoChxx(orderDetailObj)"
        class="operationButtonitem"
      >
        出货信息</view
      >
      <view
        v-if="orderDetailObj.status == 1"
        class="operationButtonitem"
        @click="trueSh(orderDetailObj)"
      >
        确认收货</view
      >
      <view
        v-if="orderDetailObj.status == 2"
        class="operationButtonitem"
        @click="gotoJsxx(orderDetailObj)"
      >
        结算信息</view
      >
    </view>
    <up-toast ref="uToastRef" :z-index="9999"></up-toast>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
const uToastRef = ref(null);
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";
const config = proxy.ipConfig;
onLoad((options) => {
  if (options && options.orderId) {
    getOrderDetail(options.orderId);
    // getchDetail(options.orderId);
  }
});
// 获取订单详情
const orderDetailObj = ref({});
const getOrderDetail = (orderId) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/farmersHome/myOrderDetail/${orderId}`,
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        orderDetailObj.value = data;
        if (
          orderDetailObj.value.status == 1 ||
          orderDetailObj.value.status == 2
        ) {
          getchDetail(orderId);
        }
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
// 跳转需求详情
function gotoXqsq(item) {
  uni.navigateTo({
    url: "/pagesPackages6/demandShView/detail?demandId=" + item.demandId,
  });
}
// 跳转议价
function gotoYjjl(item) {
  uni.navigateTo({
    url: `/pagesPackages6/demandShView/bargain?demandId=${item.demandId}&dpId=${item.dpId}`,
  });
  return;
  // #ifdef MP-WEIXIN
  uni.navigateTo({
    url: `/pagesPackages6/demandShView/bargainWx?demandId=${item.demandId}&dpId=${item.dpId}`,
  });
  // #endif
  // #ifndef MP-WEIXIN
  uni.navigateTo({
    url: `/pagesPackages6/demandShView/bargainWeb?demandId=${item.demandId}&dpId=${item.dpId}`,
  });
  // #endif
}
//跳转出货信息
function gotoChxx(item) {
  uni.navigateTo({
    url:
      "/pagesPackages3/shfishOrderView/shipmentInformation?orderId=" +
      item.orderId,
  });
}
//跳转结算信息
function gotoJsxx(item) {
  uni.navigateTo({
    url:
      "/pagesPackages3/shsettlementCenterView/details?orderId=" + item.orderId,
  });
}
//确认收货
function trueSh(item) {
  uni.showModal({
    title: "提示",
    content: "确认收货吗？",
    confirmColor: "#068324", // 确认按钮颜色
    success: (res) => {
      if (res.confirm) {
        http
          .post(
            proxy.ipConfig.baseUrl2 + `/grzxSh/qrsh`,
            {
              orderId: item.orderId,
            },
            {
              method: "POST",
            }
          )
          .then((res) => {
            if (res.data.code == 2000) {
              let postObj = {
                dpId: item.dpId,
                demandId: item.demandId,
                orderId: item.orderId,
                token: uni.getStorageSync("user")?.token,
                senderType: uni.getStorageSync("user")?.yhlx,
                status: 1,
                type: 6,
              };
              http.post(
                proxy.ipConfig.baseUrl2 + "/discussPrice/sendMessage",
                postObj,
                { method: "POST" }
              );
              uToastRef.value.show({
                type: "success",
                message: res.data.message,
              });
              setTimeout(() => {
                uni.navigateBack({ delta: 1 });
              }, 1000);
            } else {
              uToastRef.value.show({
                type: "error",
                message: res.data.message,
              });
            }
          });
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
}
// ios和安卓不同情况， 需要注意：手机号要为字符串
const cellPhone = (phone) => {
  const res = uni.getSystemInfoSync();
  // ios系统默认有个模态框
  if (res.platform == "ios") {
    uni.makePhoneCall({
      phoneNumber: phone,
    });
  } else {
    //安卓手机手动设置一个showActionSheet
    uni.showActionSheet({
      itemList: ["phone", "呼叫"],
      success: function (res) {
        if (res.tapIndex == 1) {
          uni.makePhoneCall({
            phoneNumber: phone,
          });
        }
      },
    });
  }
};
// 获取明文
const getPlainText = (phone) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/common/getPlaintext`,
      {
        content: phone,
      },
      {
        method: "POST",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        cellPhone(data);
      } else {
        uToastRef.value.show({
          type: "error",
          message: res.data.message,
        });
      }
    });
};
// 复制
const copy = (text) => {
  // 使用条件编译处理不同平台的复制
  // #ifdef H5
  const tempInput = document.createElement("textarea");
  tempInput.value = text;
  tempInput.style.position = "fixed";
  tempInput.style.opacity = 0;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand("copy");
  tempInput.remove();
  uToastRef.value.show({
    type: "success",
    title: "成功主题",
    message: "复制成功",
  });
  // #endif
  // #ifdef MP-WEIXIN
  wx.setClipboardData({
    data: text, // 或者你想要复制的内容
    success: function () {
      uToastRef.value.show({
        type: "success",
        title: "成功主题",
        message: "复制成功",
      });
    },
  });
  // #endif
};
const chDetailObj = ref({});
const getchDetail = (orderId) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/farmersHome/sendOutGoodsDetails/${orderId}`,
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        chDetailObj.value = data;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
</script>

<style lang="scss" scoped>
.detailPage {
  position: relative;
  z-index: 9;
  padding: 190rpx 0 140rpx;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  overflow: auto;
  background-color: #f3f3f3;
}

.topBg {
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  height: 246rpx;
  width: 100%;
  background: linear-gradient(65deg, #7ddb9f -7%, #f5fff9 123%);
  display: flex;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  box-sizing: border-box;

  .msgBox {
    font-family: PingFang SC;
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    padding-top: 22rpx;

    .status {
      font-size: 36rpx;
      margin-top: 4rpx;
    }
  }

  .topImg {
    width: 220rpx;
    height: 156rpx;
  }
}
//商户联系方式
.MerchantcontactInformation {
  width: 700rpx;
  min-height: 128rpx;
  border-radius: 20rpx;
  opacity: 1;
  background: #ffffff;
  margin-bottom: 24rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  .lxfs {
    height: 40rpx;
    font-family: PingFang SC;
    font-size: 32rpx;
    font-weight: 500;
    line-height: 40rpx;
    color: #333333;
    z-index: 0;
  }
  .dhhm {
    height: 40rpx;
    font-family: PingFang SC;
    font-size: 28rpx;
    font-weight: normal;
    line-height: 40rpx;
    color: #666666;
    display: flex;
    align-content: center;
    margin-top: 8rpx;
    .dhhmicon {
      width: 32rpx;
      height: 32rpx;
      margin-left: 8rpx;
    }
  }
  .address {
    min-height: 40rpx;
    font-family: PingFang SC;
    font-size: 28rpx;
    font-weight: normal;
    line-height: 40rpx;
    color: #666666;
    // display: flex;
    // align-content: center;
    vertical-align: middle; /* 垂直居中对齐 */
    margin-top: 8rpx;
    .dhhmicon {
      width: 32rpx;
      height: 32rpx;
      margin-left: 8rpx;
      vertical-align: middle; /* 垂直居中对齐 */
    }
  }
}
//订单商品
.orderGoods {
  width: 700rpx;
  // height: 160px;
  border-radius: 20rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 20rpx 30rpx;
  gap: 20rpx;
  background: #ffffff;
  z-index: 1;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  .orderGoodstitle {
    height: 40rpx;
    opacity: 1;
    font-family: PingFang SC;
    font-size: 32rpx;
    font-weight: normal;
    line-height: 40rpx;
    text-align: center;
    font-variation-settings: "opsz" auto;
    color: #333333;
    text-align: left;
  }
  .orderGoodsContent {
    .orderGoodsItem {
      height: 40rpx;
      font-family: PingFang SC;
      font-size: 26rpx;
      display: flex;
      margin-bottom: 20rpx;
      .left {
        flex: 1;
        display: flex;
      }
      .right {
        flex: 1;
        display: flex;
      }
      .label {
        /* 商品品类： */
        color: #666666;
      }
      .value {
        color: #333333;
      }
      .valuered {
        color: #ff0000;
      }
    }
  }
}
// 订单信息
.orderInfo {
  width: 700rpx;
  // height: 160px;
  border-radius: 20rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 20rpx 30rpx;
  gap: 20rpx;
  background: #ffffff;
  z-index: 1;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  .orderInfotitle {
    height: 40rpx;
    font-family: PingFang SC;
    font-size: 32rpx;
    font-weight: normal;
    line-height: 40rpx;
    color: #333333;
  }
  .orderInfocontent {
    .orderInfoitem {
      min-height: 40rpx;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      .orderInfoitemlabel {
        font-family: PingFang SC;
        font-size: 28rpx;
        font-weight: normal;
        line-height: 40rpx;
        text-align: left;
        color: #666666;
      }
      .orderInfoitemvalue2 {
        width: 70%;
        text-align: right;
      }
      .orderInfoitemvalue {
        font-family: PingFang SC;
        font-size: 28rpx;
        font-weight: normal;
        line-height: 40rpx;
        text-align: right;
        color: #333333;
        .copyImage {
          width: 32rpx;
          height: 32rpx;
          // margin-left: 10rpx;
        }
      }
    }
  }
}
//操作按钮
.operationButtons {
  /* 自动布局子元素 */
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 100rpx;
  /* 自动布局 */
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px;
  gap: 10px;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 88;
  .operationButtonitem {
    width: 148rpx;
    height: 54rpx;
    border-radius: 10rpx;
    opacity: 1;
    display: flex;
    justify-content: center;
    justify-content: space-around;
    align-items: center;
    background: #effff3;
    box-sizing: border-box;
    color: #068324;
    font-size: 24rpx;
  }
}
</style>
