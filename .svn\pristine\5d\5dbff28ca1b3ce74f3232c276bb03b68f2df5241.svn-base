<template>
  <view class="content">
    <!-- 顶部 -->
    <view
      class="navigationBar"
      :style="'background-image:url(' + config.imageUrl + '/ai/bgc.svg)'"
    >
      <view class="navigationBarback" @click="gotoBack()">
        <up-icon name="arrow-left" color="#333333" size="22"></up-icon>
      </view>
      <view class="navigationBarTitleText"> 我的贷款 </view>
      <view class="navigationBarTitleall"> </view>
    </view>
    <view class="tabs">
      <up-tabs
        :current="currentIndex"
        :scrollable="false"
        :list="list1"
        lineColor="#068324"
        lineWidth="26"
        lineHeight="4"
        :activeStyle="activeStyle"
        :inactiveStyle="inactiveStyle"
        :itemStyle="itemStyle"
        @click="changeTabs"
      ></up-tabs>
    </view>
    <view class="mallorder" id="mallorder">
      <up-list @scrolltolower="scrolltolower">
        <up-list-item
          v-for="(item, index) in [
            {},
            {},
            {},
            {},
            {},
            {},
            {},
            {},
            {},
            {},
            {},
            {},
            {},
            {},
            {},
          ]"
          :key="index"
          class="mallorder-card-fa"
        >
          <!-- //我的贷款 -->
          <view class="mydk-card">
            <view class="mydk-card-top">
              <view class="mydk-card-top-left">
                <image
                  :src="config.imageUrl + '/myPage/yh.svg'"
                  class="mydk-card-top-left-img"
                ></image>
              </view>
              <view class="mydk-card-top-mid">
                <view class="mydk-card-top-mid-title">
                  邮储银行-邮易贷-极速贷
                </view>
                <view class="mydk-card-top-mid-tag">
                  <view class="mydk-card-top-mid-tag-item"> 信用</view>
                  <view class="mydk-card-top-mid-tag-item"> 抵押</view>
                </view>
              </view>
              <view class="mydk-card-top-right">
                <view
                  v-if="item.status == 51"
                  class="mydk-card-top-right-status"
                >
                  待受理
                </view>
                <view
                  v-if="item.status == 1"
                  class="mydk-card-top-right-status mydk-card-top-right-status1"
                >
                  待审核
                </view>
                <view
                  v-if="item.status == 2"
                  class="mydk-card-top-right-status mydk-card-top-right-status2"
                >
                  待放款
                </view>
                <view
                  v-if="item.status == 3"
                  class="mydk-card-top-right-status mydk-card-top-right-status3"
                >
                  已放款
                </view>
                <view
                  v-if="item.status == 60"
                  class="mydk-card-top-right-status mydk-card-top-right-status60"
                >
                  不受理
                </view>
              </view>
            </view>
            <view class="mydk-card-content">
              <view class="mydk-card-content-item">
                <view class="mydk-card-content-item-left">申请金额(万元) </view>
                <view class="mydk-card-content-item-right"> 500,000</view>
              </view>
              <view class="mydk-card-content-item">
                <view class="mydk-card-content-item-left">申请期限(月) </view>
                <view class="mydk-card-content-item-right"> 12个月</view>
              </view>
              <view class="mydk-card-content-item">
                <view class="mydk-card-content-item-left">贷款申请编号 </view>
                <view class="mydk-card-content-item-right">
                  {{ item.orderNo }}</view
                >
              </view>
              <view class="mydk-card-content-item">
                <view class="mydk-card-content-item-left">贷款申请时间 </view>
                <view class="mydk-card-content-item-right">{{
                  item.xdTime
                }}</view>
              </view>
            </view>
            <view class="mydk-card-bom">
              <view class="mydk-card-bom-item mydk-card-bom-item-de">
                查看详情
              </view>
              <view
                v-if="item.status == 1 || item.status == 2 || item.status == 3"
                class="mydk-card-bom-item"
              >
                查看进度
              </view>
            </view>
          </view>
        </up-list-item>
      </up-list>
      <!-- <up-empty v-else mode="order" :icon="`${config.imageUrl}'/mallOrder/noorder.svg`">
      </up-empty> -->
      <!-- <up-empty
        width="700rpx"
        v-else
        text=" "
        icon="http://*************:9754/sy-images/mallOrder/noorderbg.svg"
      >
      </up-empty> -->
    </view>
  </view>
  <up-toast ref="uToastRef" :z-index="9999"></up-toast>
  <up-notify message="功能开发中..." ref="uNotifyRef"></up-notify>

  <up-modal
    :title="titleModal"
    :content="contentModal"
    :show="showModal"
    showCancelButton
    closeOnClickOverlay
    @confirm="confirmModal"
    @cancel="cancelModal"
    @close="closeModal"
    ref="uModal"
  ></up-modal>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, reactive, onMounted, watch, computed } from "vue";

// 导入ipConfig
import { getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
const { proxy } = getCurrentInstance();
const route = useRoute();
// 导入http
import { http, toast } from "uview-plus";
const uToastRef = ref(null);
const uNotifyRef = ref(null);
var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl1;
}

//顶部tab
// 创建响应式数据
const list1 = reactive([
  { name: "全部", status: "" },
  { name: "待受理", status: 51 },
  { name: "待审核", status: 1 },
  { name: "待放款", status: 2 },
  { name: "已放款", status: 99 },
]);
let activeStyle = ref({
  // fontWeight: 'bold',
  fontSize: "32rpx",
  width: "70px",
  // backgroundColor: '#E6F7FF',
  height: "48px",
  lineHeight: "48px",
  textAlign: "center",
  paddingBottom: "3px",
  // color: '#068324',
  color: "#333333",
  transform: "scale(1.05)",
});
let inactiveStyle = ref({
  width: "70px",
  color: "#666666",
  fontSize: "32rpx",
  // fontWeight: 'bold',
});
let itemStyle = ref({
  width: "70px",
  height: "48px",
  padding: "0px",
});
// 定义方法
let currentIndex = ref(0);
let pageNumber = ref(1); //初始页数
let isHasdata = ref(true);
let status = ref(0);
function changeTabs(item) {
  window.document.getElementById("mallorder").scrollTop = 0;
  wx.pageScrollTo({
    scrollTop: 0,
    duration: 300, // 滚动动画时长（ms）
  });
  status.value = item.status;
  currentIndex.value = item.index;
  pageNumber.value = 1;
  mallOrderData.value = [];
  getmallOrderList(item.status);
}

//底部触发
const scrolltolower = () => {
  return;
  if (isHasdata.value) {
    pageNumber.value++;
    getmallOrderList(status.value);
  }
};

// 获取订单数据
let mallOrderData = ref([]);
//跳转订单详情
const gotoOrderdetail = (item) => {
  let { orderId, status, sfsqsh } = item;
  if (orderId && status == 2 && sfsqsh == 0) {
    uni.navigateTo({
      url: `/pagesPackages3/orderDetailView/index?orderId=${orderId}`,
    });
  } else if (orderId && status != 2) {
    if (status == 4 || status == 5 || status == 6 || status == 7) {
      uToastRef.value.show({
        type: "error",
        icon: false,
        title: "失败主题",
        message: "页面正在设计中..",
      });
    } else {
      uni.navigateTo({
        url: `/pagesPackages3/orderDetailView/other?orderId=${orderId}`,
      });
    }
  } else {
  }
};

getConfig();

onMounted(() => {
  // getmallOrderList(currentIndex.value)
  pageNumber.value = 1;
});
// 获取订单数据
const getmallOrderList = (status) => {
  mallOrderData.value = [];
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/store/order/findAll",
      {
        status: status,
        // page: pageNumber.value,
        // pageSize: 5,
      },
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        if (data && data.length) {
          mallOrderData.value = data.map((item) => ({
            ...item,
            remainingTime: calculateRemainingTime(item.xdTime), // 新增剩余时间字段
          }));
          // 启动全局倒计时
          startGlobalTimer();
          // mallOrderData.value = mallOrderData.value.concat(res.data)
          // if (mallOrderData.value.length < res.total) {
          //   isHasdata.value = true
          // } else {
          //   isHasdata.value = false
          // }
        }
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
// 新增倒计时计算方法
const calculateRemainingTime = (xdTime) => {
  // const endTime = new Date('2025-04-21 15:22:00').getTime()
  const endTime = new Date(xdTime).getTime();
  const now = Date.now();
  return Math.max(0, Math.floor((endTime - now) / 1000) + Number(1800));
};
// 新增全局倒计时
let globalTimer = null;
const startGlobalTimer = () => {
  clearInterval(globalTimer);
  globalTimer = setInterval(() => {
    mallOrderData.value = mallOrderData.value.map((item) => ({
      ...item,
      remainingTime: item.remainingTime > 0 ? item.remainingTime - 1 : 0,
    }));
  }, 1000);
};
// 修改时间转换方法
const convertTime = (seconds, orderId) => {
  if (seconds == 1) {
    //取消订单
    cancelOrderTrue(orderId);
  }
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
};
let showModal = ref(false);
let titleModal = ref("");
let contentModal = ref("");
let cancelOrderId = ref("");
//取消订单
const cancelOrder = (orderId, status) => {
  cancelOrderId.value = orderId;
  if (status == 0) {
    contentModal.value = "订单取消后无法恢复,如您需要可再次加购后结算";
  }
  if (status == 1) {
    contentModal.value =
      "订单取消后无法恢复,退款将于1-3个工作日原路返回您的账户";
  }
  showModal.value = true;
};
//取消订单
const cancelOrderTrue = (orderId) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/store/order/cancel/${orderId}`,
      {
        orderId: orderId,
      },
      {
        method: "POST",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        uToastRef.value.show({
          type: "success",
          title: "成功主题",
          message: "取消订单已发起,请等待商家反馈",
        });
        getmallOrderList(status.value);
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};

//弹出层
const confirmModal = () => {
  showModal.value = false;
  cancelOrderTrue(cancelOrderId.value);
};
const cancelModal = () => {
  showModal.value = false;
};
const closeModal = () => {};
// 确认收货
const confirmReceipt = (orderId) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/store/order/sure/receive/${orderId}`,
      {
        orderId: orderId,
      },
      {
        method: "POST",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        uToastRef.value.show({
          type: "success",
          title: "成功主题",
          message: "操作成功",
        });
        getmallOrderList(status.value);
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
// 申请退款
let orderId = ref("");
// 售后方式 1仅退款 2退货退款
let thArr = ref([
  {
    type: 1,
    name: "仅退款",
  },
  {
    type: 2,
    name: "退货退款",
  },
]);
let currentScaleIndex = ref(1);
let shDetailVos = ref([]);
let shDzglVo = ref(null);
const refundApplication = (item) => {
  orderId.value = item.orderId;
  shDzglVo.value = item.dzglVo;
  shDetailVos.value = item.detailVos.map((item) => ({
    ...item,
    checked: false,
    itemCount: 0,
  }));
  showPopup.value = true;
};
const changeItem = (item) => {
  shDetailVos.value.forEach((el) => {
    if (item.detailId == el.detailId) {
      el.checked = !item.checked;
    }
  });
};
//退款弹出层
const showPopup = ref(false);
const scaleClick = (item, index) => {
  currentScaleIndex.value = index;
};
//退款下一步
const next = () => {
  let tkparams = {
    orderId: orderId.value,
    type: thArr.value[currentScaleIndex.value].type,
    shDetailVos: shDetailVos.value.filter((el) => {
      if (el.checked && el.itemCount > 0) {
        return el;
      }
    }),
    shDzglVo: shDzglVo.value,
  };
  uni.setStorageSync("tkparams", tkparams);
  if (tkparams.shDetailVos.length == 0) {
    return uToastRef.value.show({
      type: "error",
      title: "失败主题",
      message: "请选择商品且商品数量不能为0",
    });
  }
  if (currentScaleIndex.value == 0) {
    // 仅退款
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/refundOnly?orderId=${orderId.value}&currentIndex=${currentIndex.value}`,
    });
  }
  if (currentScaleIndex.value == 1) {
    // 退款退货
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/index?orderId=${orderId.value}&currentIndex=${currentIndex.value}`,
    });
  }
  showPopup.value = false;
};
// 申请开票
const invoiceApplication = (orderId) => {
  uToastRef.value.show({
    type: "error",
    icon: false,
    title: "失败主题",
    message: "功能正在开发中",
  });
  // uni.navigateTo({
  //   url: `/pagesPackages3/refundAndReturn/requestInvoice?orderId=${orderId}`
  // });
};
// 售后详情
const afterSalesDetails = (orderId) => {
  uni.navigateTo({
    url: `/pagesPackages3/refundAndReturn/afterSalesCompleted?orderId=${orderId}`,
  });
};
//撤回售后
const recallAfterSales = (orderId, showBtn) => {
  uni.navigateTo({
    url: `/pagesPackages3/refundAndReturn/afterSalesDetails?orderId=${orderId}&currentIndex=${currentIndex.value}&showBtn=${showBtn}`,
  });
};
// 定义时间格式化函数
const formatDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
// 立即支付
const confirmPayment = (item) => {
  let params = {
    orderId: item.orderId,
    type: 1,
    orderNo: item.orderNo,
    payTime: formatDateTime(),
    amount: item.amount,
    // "appid": "string",
    // "mchid": "string",
    // "outTradeNo": "string",
    // "openid": "string"
  };
  http
    .post(proxy.ipConfig.baseUrl2 + `/store/order/pay`, params, {
      method: "POST",
    })
    .then((res) => {
      if (res.data.code == 2000) {
        uni.navigateTo({
          url: `/pagesPackages2/shopCartView/components/paymentSuccess?status=3&totalPrice=${item.amount}`,
        });
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
// 查看物流
const trackShipment = () => {
  uToastRef.value.show({
    type: "error",
    icon: false,
    title: "失败主题",
    message: "功能正在开发中",
    iconUrl: "https://uview-plus.jiangruyi.com/resources/toast/error.png",
  });
};
onLoad((options) => {
  currentIndex.value = Number(options.currentIndex) || 0;
  status.value = Number(options.currentIndex)
    ? Number(options.currentIndex) - 1
    : "";
  getmallOrderList(status.value == 3 ? "99" : status.value);
});
// 新增onShow生命周期
onShow(() => {
  const refreshFlag = uni.getStorageSync("needRefresh");
  if (refreshFlag) {
    getmallOrderList(status.value == 3 ? "99" : status.value); // 重新获取数据
    uni.removeStorageSync("needRefresh");
  }
});
// 跳转售后说明
const gotoShsm = () => {
  uni.navigateTo({
    url: `/pagesPackages3/helpAndfeedbackView/supportGuidelines`,
  });
};
// 返回
const gotoBack = () => {
  uni.redirectTo({
    url: "/pagesPackages3/myPageView/index",
  });
};
</script>

<style scoped>
.content {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  width: 100vw;
  height: 100%;
  overflow: hidden;
  background-color: #f3f3f3;
  font-family: PingFang SC;
}

.tabs {
  width: 100%;
  height: 83.72rpx;
  text-align: center;
  background-color: #fff;
  padding-top: 188rpx;
  padding-bottom: 2px;
}

.mallorder {
  box-sizing: border-box;
  width: 100%;
  height: 90vh;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  overflow: auto;
  padding-bottom: 20.93rpx;
}

.mallorder-card-fa {
  width: 100%;
  display: flex;
  justify-content: center;
}
.u-list-item {
  width: 100%;
  display: flex;
  justify-content: center;
}
:deep(.u-list-item) {
  width: 100%;
  display: flex;
  justify-content: center;
}
.mallorder-card {
  width: 711.63rpx;
  /* height: 408.14rpx; */
  border-radius: 17.44rpx;
  background-color: rgba(255, 255, 255, 1);
  color: rgba(16, 16, 16, 1);
  box-shadow: 0px 1.74rpx 8.72rpx 0px rgba(202, 204, 204, 0.2);
  border: 1.74rpx solid rgba(0, 0, 0, 0.07);
  margin-top: 20.93rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.up-avatarImage {
  width: 100%;
  height: 100%;
  border-radius: 6.98rpx;
}

/* 按钮 */
.buttom {
  width: 180rpx;
  height: 69.77rpx;
  line-height: 69.77rpx;
  border-radius: 17.44rpx;
  font-size: 24.42rpx;
  text-align: center;
  font-family: PingFang SC;
  box-sizing: border-box;
}

/* 基本 */
.buttom-primary {
  background-color: #f4f4f4;
  color: rgba(0, 0, 0, 1);
}

.buttom-sh {
  background-color: #fff;
  border: 1px #ff3132 solid;
  color: #ff3132;
}

.buttom-cancle {
  background-color: #f4f4f4;
  color: rgba(0, 0, 0, 1);
  /* border: 3.49rpx dotted rgba(155, 155, 155, 1); */
}

/* 支付 */
.buttom-apply {
  background-color: #ffdddd;
  color: #ff3132;
}

/* 申请退款 */
.popupBox {
  position: relative;
  padding: 40rpx 24rpx 0;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx) ;
  background-color: #f5f5f5;
  max-width: 100vw;
  box-sizing: border-box;
}

.popup_title {
  width: 100%;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #333333;
}

.popup_type {
  width: 100%;
  height: 174rpx;
  border-radius: 10px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 20rpx 24rpx;
  background: #ffffff;
  box-sizing: border-box;
  margin-top: 32rpx;
}

.popup_main {
  border-radius: 10px;
  padding: 20rpx 24rpx 40rpx;
  background: #ffffff;
  box-sizing: border-box;
  margin-top: 24rpx;
  max-height: 60vh;
  overflow: auto;
}

.popup_type_title {
  width: 100%;
  height: 50rpx;
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.popup_spec {
  width: 100%;
  display: flex;
  margin-top: 20rpx;
  flex-wrap: nowrap;
}

.popup_spec-2 {
  flex-wrap: wrap;
}

.popup_scaleItem {
  min-width: 160rpx;
  height: 64rpx;
  border-radius: 192rpx;
  opacity: 1;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #f4f4f4;
  z-index: 1;
  color: #333333;
  margin-right: 24rpx;
  box-sizing: border-box;
}

.activeScale {
  background: rgba(255, 49, 50, 0.08);
  border: 1px solid rgba(255, 49, 50, 0.5);
  color: #ff3132;
}

.orderDetail-orderList-item {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  /* flex-wrap: wrap; */
  margin: 10rpx 0;
}

.orderDetail-orderList-item-img {
  width: 156rpx;
  height: 156rpx;
  margin-right: 20rpx;
}

.orderDetail-orderList-item-content {
  margin-left: 20rpx;
  width: 444rpx;
  height: 156rpx;
  display: flex;
  align-content: space-between;
  flex-wrap: wrap;
  margin-left: auto;
}

.orderDetail-orderList-item-content-title {
  width: 100%;
  max-height: 69.77rpx;
  line-height: 34.88rpx;
  display: flex;
  align-items: flex-start;
  color: rgb(16, 16, 16);
  font-size: 24.42rpx;
  font-weight: bold;
  text-align: left;
}

.mallorder-card-center-details-tag {
  display: flex;
}

.titleLv {
  width: 84rpx;
  height: 24rpx;
  border-radius: 4rpx;
  padding: 4rpx;
  background: #20b203;
  color: #fff;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 24rpx;
  margin-right: 8rpx;
  text-align: center;
  margin-right: 5rpx;
}

.orderDetail-orderList-item-content-title .titletext {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  font-size: 28rpx;
}

.orderDetail-orderList-item-content-weight {
  height: 34.88rpx;
  line-height: 34.88rpx;
  color: rgba(16, 16, 16, 0.59);
  font-size: 24.42rpx;
  text-align: left;
  /* 省略 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.orderDetail-orderList-item-content-price {
  width: 100%;
  height: 69.77rpx;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.orderDetail-orderList-item-content-price-left {
  width: 174.42rpx;
  line-height: 69.77rpx;
  height: 69.77rpx;
  color: rgba(255, 66, 66, 1);
  font-size: 18px;
}

.orderDetail-orderList-item-content-price-right {
  /* width: 139.53rpx; */
  height: 34.88rpx;
  line-height: 34.88rpx;
  color: #666666;
  font-size: 24.42rpx;
  text-align: right;
}

.orderDetail-orderList-item-allPrice {
  width: 100%;
  height: 48.84rpx;
  display: flex;
  justify-content: flex-end;
  line-height: 48.84rpx;
  font-size: 16px;
}

.orderDetail-orderList-item-text {
  color: rgba(155, 155, 155, 1);
}

.orderDetail-orderList-item-num {
  color: #ff4242;
  font-weight: bold;
}

.afterSalesInfo {
  text-align: center;
  width: 100%;
  height: 50rpx;
  line-height: 50rpx;
  margin-bottom: 140rpx;
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-content: center;
  justify-content: center;
  margin-top: 20rpx;
}

.popup_bottomBtn {
  width: 100%;
  display: flex;
  justify-content: center;
}

.normalButton {
  width: 700rpx;
  height: 80rpx;
  border-radius: 306rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  gap: 20rpx;
  box-sizing: border-box;
  background: #068324;
  z-index: 0;
  color: #ffffff;
}

.up-toast {
  z-index: 9999 !important;
}
.u-empty {
  display: flex;
  align-content: flex-start;
  align-items: flex-start;
  padding-top: 24rpx;
  justify-content: flex-start;
}
/* 我的贷款 */
.mydk-card {
  width: 700rpx;
  min-height: 229px;
  border-radius: 20rpx;
  opacity: 1;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  gap: 24rpx;
  background: #ffffff;
  z-index: 0;
  box-sizing: border-box;
  margin-top: 24rpx;
}
.mydk-card-top {
  width: 100%;
  height: 86rpx;
  display: flex;
  justify-content: flex-start;
  align-content: center;
  align-content: center;
}
.mydk-card-top-left {
  width: 80rpx;
  height: 80rpx;
  margin-right: 12rpx;
}
.mydk-card-top-left-img {
  width: 100%;
  height: 100%;
}
.mydk-card-top-mid {
  width: 360rpx;
  height: 86rpx;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mydk-card-top-mid-title {
  width: 100%;
  height: 44rpx;
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  font-variation-settings: "opsz" auto;
  color: #333333;
  z-index: 0;
}
.mydk-card-top-mid-tag {
  width: 100%;
  height: 40rpx;
  display: flex;
  align-content: center;
  align-items: center;
}
.mydk-card-top-mid-tag-item {
  width: 80rpx;
  height: 38rpx;
  border-radius: 4rpx;
  opacity: 1;
  box-sizing: border-box;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #f5f5f5;
  opacity: 1;
  font-size: 24rpx;
  font-weight: normal;
  font-variation-settings: "opsz" auto;
  color: #666666;
  z-index: 0;
  margin-right: 8rpx;
}
.mydk-card-top-right {
  margin-left: auto;
  height: 44rpx;
  display: flex;
  align-content: center;
  align-items: center;
}
.mydk-card-top-right-status {
  width: 104rpx;
  height: 38rpx;
  border-radius: 4rpx;
  opacity: 1;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border: 1rpx solid #ffb13d;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-variation-settings: "opsz" auto;
  color: #ffb13d;
}
.mydk-card-top-right-status1 {
  border: 1rpx solid #ffb13d;
  color: #ffb13d;
}
.mydk-card-top-right-status2 {
  border: 1rpx solid #ffb13d;
  color: #ffb13d;
}
.mydk-card-top-right-status3 {
  border: 1rpx solid #068324;
  color: #068324;
}
.mydk-card-top-right-status51 {
  border: 1rpx solid #7900ff;
  color: #7900ff;
}
.mydk-card-top-right-status60 {
  border: 1rpx solid #ff3132;
  color: #ff3132;
}
.mydk-card-content {
  width: 100%;
  height: auto;
  /* margin: 24rpx 0; */
  border-bottom: 1rpx solid rgba(17, 31, 44, 0.12);
}
.mydk-card-content-item {
  /* 自动布局子元素 */
  width: 100%;
  height: 40rpx;
  opacity: 1;
  font-family: PingFang SC;
  font-size: 28rpx;
  color: #999999;
  display: flex;
  align-content: center;
  justify-content: space-between;
  text-align: left;
  line-height: 40rpx;
  margin-bottom: 12rpx;
}
.mydk-card-content-item-left {
}
.mydk-card-content-item-right {
  color: #333333;
  text-align: right;
}
.mydk-card-bom {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: flex-end;
}
.mydk-card-bom-item {
  width: 148rpx;
  height: 54rpx;
  border-radius: 10rpx;
  opacity: 1;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border: 1rpx solid #068324;
  margin-left: 20rpx;
  font-family: PingFang SC;
  font-size: 28rpx;
  color: #068324;
}
.mydk-card-bom-item-de {
  background: #068324;
  color: #ffffff;
}
.navigationBar {
  width: 100%;
  height: 176rpx;

  display: flex;
  justify-content: space-between;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  /* background-image: url(/images/ai/bgc.svg); */
  background-size: cover;
  background-repeat: no-repeat;
  padding-top: 88rpx;
  box-sizing: border-box;
}

.navigationBarback {
  height: 88rpx;
  line-height: 88rpx;
  opacity: 1;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  display: flex;
  align-items: center;
  padding-left: 24rpx;
  position: relative;
  z-index: 99;
}

.navigationBarTitleText {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  opacity: 1;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 11;
}
:deep(.orderDetail-orderList-item-content-price-right .uni-input-input) {
  font-size: 22rpx;
}
</style>
