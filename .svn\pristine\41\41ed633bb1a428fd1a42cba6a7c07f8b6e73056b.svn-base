<template>
  <view class="content">
    <view class="List" v-for="(items, index) in shDetailVos" :key="index">
      <view class="orderDetail-orderList">
        <view class="orderDetail-orderList-item">
          <view class="orderDetail-orderList-item-img">
            <image class="up-avatarImage" :src="items.productVo.pic"> </image>
          </view>
          <view class="orderDetail-orderList-item-content">
            <view class="orderDetail-orderList-item-content-title">
              <view class="mallorder-card-center-details-tag">
                <text
                  v-for="(tag, index) in items.tagVos"
                  :key="index"
                  class="titleLv"
                  :style="`backgroundColor: ${tag.color}`"
                >
                  {{ tag.name }}
                </text>
              </view>
              <text class="titletext"> {{ items.productVo.name }} </text>
            </view>
            <view class="orderDetail-orderList-item-content-weight">
              规格: {{ items.productVo.spData }}
            </view>
            <view class="orderDetail-orderList-item-content-price">
              <view class="orderDetail-orderList-item-content-price-left">
                ¥{{ items.amount }}
              </view>
              <view class="orderDetail-orderList-item-content-price-right">
                ×{{ items.itemCount }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="orderDetail-orderInfo">
        <view class="formItem">
          <view class="label"> <view class="require"> *</view> 申请原因 </view>
          <view class="input">
            <!-- <up-textarea v-model="items.reason" maxlength="500" border="none" placeholder="请输入申请原因"></up-textarea> -->
            <up-input
              maxlength="500"
              v-model="items.reason"
              border="none"
              placeholder="请输入申请原因"
              @change="changeFn"
            ></up-input>
          </view>
        </view>
        <view class="formItem" @click="showFilePopupFn(items.detailId)">
          <view class="label">
            <view class="require"> *</view> 问题描述与凭证
          </view>
          <view class="input">
            <up-input
              ref="remarkInput"
              maxlength="500"
              v-model="items.remark"
              border="none"
              placeholder="请您务必上传描述与凭证"
              @change="changeFn"
            >
            </up-input>
            <view class="upload">
              <up-icon name="arrow-right" size="18"></up-icon>
              <!-- @click="afterRead(items.detailId)" -->
            </view>
          </view>
          <view class="fileList">
            <view
              class="fileItem"
              v-for="(item, index) in items.sourceDtos"
              :key="index"
            >
              <view class="fileItemImg">
                <image
                  class="up-avatarImage"
                  @click="previewImage(items.sourceDtos, index)"
                  :src="item.allPath"
                ></image>
              </view>
              <view class="delImg" @click="delImage(item, index)">
                <up-icon name="close" color="red" size="14"></up-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="orderDetail-orderInfo-2">
      <view class="shfs">
        <view class="shfsLeft"> 返回方式 </view>
        <view class="shfsRight"> 上门取件 </view>
      </view>
      <view class="shfs">
        <view class="shfsLeft"> 取件地址 </view>
        <view class="shfsAddress" @click.stop="gotoEditadd">
          <view class="shfsAddressT">
            {{ shDzglVo.name }} {{ shDzglVo.phone }}
          </view>
          <view class="shfsAddressM">
            {{ shDzglVo.province }} {{ shDzglVo.city }} {{ shDzglVo.district }}
          </view>
          <view class="shfsAddressB">
            {{ shDzglVo.detailAddress }}
          </view>
          <view class="upIcon">
            <up-icon name="arrow-right" size="18"></up-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="orderDetail-orderInfo-btn">
      <view @click.stop="requestRefund()" class="buttom-sub"> 提交 </view>
    </view>
  </view>
  <up-toast ref="uToastRef"></up-toast>
  <!-- 上传描述和凭证 -->
  <up-popup
    :z-index="101"
    :show="showFilePopup"
    bgColor="transparent"
    @close="showFilePopup = false"
  >
    <view class="showFilePopup">
      <!-- 标题 -->
      <view class="popup_title">
        <text>上传描述与凭证</text>
        <view class="popup_title_close">
          <image
            class="popup_title_close_mage"
            @click="showFilePopup = false"
            :src="config.imageUrl + '/mallOrder/close.svg'"
          >
          </image>
        </view>
      </view>
      <!-- 问题描述 -->
      <view class="formItem">
        <view class="label">
          <view class="require"> *</view> 问题描述与凭证
        </view>
        <view class="input">
          <up-textarea
            v-model="showFileData.remark"
            placeholder="请描述你遇到的问题"
            autoHeight
            border="none"
          ></up-textarea>
        </view>
      </view>
      <!-- 上传凭证 -->
      <view class="formItem">
        <view class="label">
          <view class="require"> *</view> 上传凭证
          <view class="number"> 最多九张</view>
        </view>
        <view class="file">
          <view class="upload" @click="afterRead(showFileData.detailId)">
            <image
              :src="config.imageUrl + '/mallOrder/upload.svg'"
              class="upload-img"
            ></image>
          </view>
          <view
            class="fileItem"
            v-for="(item, index) in showFileData.sourceDtos"
            :key="index"
          >
            <view class="fileItemImg">
              <image
                class="up-avatarImage"
                @click="previewImage(showFileData.sourceDtos, index)"
                :src="item.allPath"
              ></image>
            </view>
            <view class="delImg" @click="delImage(item, index)">
              <up-icon name="close" color="red" size="14"></up-icon>
            </view>
          </view>
        </view>
      </view>
      <!-- 按钮 -->
      <view class="popup_bottomBtn">
        <view
          @click="sureFilePopu"
          class="popup_bottomBtn_item popup_bottomBtn_item_query"
          >确定
        </view>
      </view>
    </view>
  </up-popup>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
// 导入http
import { http, toast } from "uview-plus";
const uToastRef = ref(null);
var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl1;
}

// 复制
const copy = (text) => {
  // 使用条件编译处理不同平台的复制
  // #ifdef H5
  const tempInput = document.createElement("textarea");
  tempInput.value = text;
  tempInput.style.position = "fixed";
  tempInput.style.opacity = 0;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand("copy");
  tempInput.remove();
  uToastRef.value.show({
    type: "success",
    title: "成功主题",
    message: "复制成功",
  });
  // #endif
  // #ifdef MP-WEIXIN
  wx.setClipboardData({
    data: text, // 或者你想要复制的内容
    success: function () {
      uToastRef.value.show({
        type: "success",
        title: "成功主题",
        message: "复制成功",
      });
    },
  });
  // #endif
};

getConfig();

// 加载
let orderId = ref("");
let orderDetail = ref(null);
let tkparams = ref(null);
let shDetailVos = ref([]);
let shDzglVo = ref(null);
let fileList = ref([]);
let currentIndex = ref("");
let refundOrAfterSale = ref("");
onLoad((options) => {
  orderId.value = options.orderId;
  refundOrAfterSale.value = options.refundOrAfterSale;
  tkparams.value = uni.getStorageSync("tkparams");
  currentIndex.value = options.currentIndex;
  if (uni.getStorageSync("shDetailVos")) {
    shDetailVos.value = JSON.parse(uni.getStorageSync("shDetailVos"));
    uni.removeStorageSync("shDetailVos");
  } else {
    shDetailVos.value = tkparams.value.shDetailVos.map((items) => {
      return {
        ...items,
        reason: items.reason || "",
        remark: items.remark || "",
        sourceDtos: items.sourceDtos || [],
      };
    });
  }
  shDzglVo.value = tkparams.value.shDzglVo;
  if (uni.getStorageSync("addressData")) {
    shDzglVo.value = {
      ...shDzglVo.value,
      ...JSON.parse(uni.getStorageSync("addressData")),
    };
    uni.removeStorageSync("addressData");
    upDateCartAddress(shDzglVo.value);
    return;
  }
});
onShow(() => {
  if (uni.getStorageSync("addressData")) {
    shDzglVo.value = {
      ...shDzglVo.value,
      ...JSON.parse(uni.getStorageSync("addressData")),
    };
    uni.removeStorageSync("addressData");
    upDateCartAddress(shDzglVo.value);
    return;
  }
  if (uni.getStorageSync("refundOrAfterSale")) {
    refundOrAfterSale.value = JSON.parse(
      uni.getStorageSync("refundOrAfterSale")
    );
    uni.removeStorageSync("refundOrAfterSale");
    return;
  }
});
//修改订单收货地址
const upDateCartAddress = (item) => {
  // 接口错误可能换接口这是订单接口
  return;
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/store/order/dzgl/update",
      {
        ...item,
      },
      {
        method: "POST",
      }
    )
    .then((res) => {});
};
// 附件上传后的操作
function afterRead(detailId) {
  // 获取当前商品已上传图片数量
  const currentProduct = shDetailVos.value.find(
    (el) => el.detailId === detailId
  );
  const currentCount = currentProduct?.sourceDtos?.length || 0;

  if (currentCount >= 9) {
    uToastRef.value.show({ type: "error", message: "最多上传9张凭证图片" });
    return;
  }
  uni.chooseImage({
    count: 9 - currentCount, //
    sizeType: ["original", "compressed"], // 可以指定是原图还是压缩图，默认二者都有
    sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有
    success: (res) => {
      // 二次校验防止异步操作导致超额
      if (currentProduct.sourceDtos.length + res.tempFiles.length > 9) {
        uToastRef.value.show({ type: "error", message: "超出最大上传数量" });
        return;
      }
      res.tempFiles.forEach((file, index) => {
        // 使用条件编译处理不同平台的滚动
        // #ifdef H5
        uni.uploadFile({
          url: proxy.ipConfig.baseUrl2 + "/common/upload",
          file: file,
          name: "files",
          formData: {},
          header: {
            Token: uni.getStorageSync("user").token,
            // 'content-type': 'multipart/form-data'
          },
          success: (uploadFileRes) => {
            let obj = JSON.parse(uploadFileRes.data);
            if (obj.code === 2000) {
              shDetailVos.value.forEach((el) => {
                if (el.detailId == detailId) {
                  el.sourceDtos.push(obj.data[0]);
                }
              });
            }
          },
        });
        // #endif
        // #ifdef MP-WEIXIN
        uni.uploadFile({
          url: proxy.ipConfig.baseUrl2 + "/common/upload",
          // file: file,
          filePath: file.path,
          name: "files",
          formData: {},
          header: {
            Token: uni.getStorageSync("user").token,
            // 'content-type': 'multipart/form-data'
          },
          success: (uploadFileRes) => {
            let obj = JSON.parse(uploadFileRes.data);
            if (obj.code === 2000) {
              shDetailVos.value.forEach((el) => {
                if (el.detailId == detailId) {
                  el.sourceDtos.push(obj.data[0]);
                }
              });
            }
          },
          fail: (err) => {
          },
        });
        // #endif
      });
    },
  });
}
// 修改地址
const gotoEditadd = () => {
  uni.setStorageSync(
    "refundOrAfterSale",
    JSON.stringify(refundOrAfterSale.value)
  );
  uni.setStorageSync("shDetailVos", JSON.stringify(shDetailVos.value || ""));
  uni.setStorageSync("addressData", JSON.stringify(shDzglVo.value || ""));
  uni.navigateTo({
    url: `/pagesPackages2/receiveAddressView/index?from=orderDetail&url=/pagesPackages3/refundAndReturn/index&orderId=${orderId.value}`,
  });
};

// 申请退款
const requestRefund = () => {
  for (const el of shDetailVos.value) {
    if (!el.reason?.trim()) {
      uToastRef.value.show({ type: "error", message: "请输入申请原因" });
      return;
    }
    if (!el.remark?.trim()) {
      uToastRef.value.show({ type: "error", message: "请输入问题描述" });
      return;
    }
    if (el.sourceDtos.length === 0) {
      uToastRef.value.show({ type: "error", message: "请输入问题凭证" });
      return;
    }
  }
  let params = {
    orderId: tkparams.value.orderId,
    type: tkparams.value.type,
    reason: "",
    lxfs: "",
    dzglId: tkparams.value.shDzglVo.dzglId,
    shhProductDtos: shDetailVos.value.map((el) => {
      return {
        productId: el.productId,
        nums: el.itemCount,
        skuId: el.skuId,
        detailId: el.detailId,
        // "orders": el.orders,
        reason: el.reason,
        // "lxfs": el.lxfs,
        // "fhfs": el.fhfs,
        amount: el.amount,
        remark: el.remark,
        sourceDtos: el.sourceDtos,
      };
    }),
  };
  let url = "";
  if (refundOrAfterSale.value == "refund") {
    url = `${proxy.ipConfig.baseUrl2}/store/order/apply/reback/pay`;
  }
  if (refundOrAfterSale.value == "aftersale") {
    url = `${proxy.ipConfig.baseUrl2}/store/order/apply/shh`;
  }
  http
    .post(url, params, {
      method: "POST",
    })
    .then((res) => {
      if (res.data.code == 2000) {
        uToastRef.value.show({
          type: "success",
          title: "成功主题",
          message: "操作成功",
        });
        // 返回
        uni.setStorageSync("needRefresh", true);
        setTimeout(() => {
          uni.navigateTo({
            url: `/pagesPackages3/mallOrderView/index?currentIndex=${currentIndex.value}`,
          });
        }, 800);
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
// 图片预览方法
const previewImage = (urls, currentIndex) => {
  uni.previewImage({
    urls: urls.map((item) => item.allPath),
    current: urls[currentIndex].allPath,
  });
};
// 删除图片
const delImage = (file, index) => {
  uni.showModal({
    title: "删除确认",
    content: "确定要删除这张凭证图片吗？",
    success: (res) => {
      if (res.confirm) {
        shDetailVos.value = shDetailVos.value.map((product) => {
          if (product.sourceDtos.includes(file)) {
            return {
              ...product,
              sourceDtos: product.sourceDtos.filter((_, i) => i !== index),
            };
          }
          return product;
        });
        uToastRef.value.show({ type: "success", message: "删除成功" });
      }
    },
  });
};
const changeFn = (e) => {
  if (e.length >= 500) {
    uToastRef.value.show({
      type: "error",
      message: "最大支持500个字,已超出",
    });
  }
};
// new-------------------------------
// 问题描述与凭证弹出层
const showFilePopup = ref(false);
const textPopup = ref("");
// 弹出层
let showFileData = ref({});
let remarkInput = ref(null);
let detailIdPopup = ref("");
const showFilePopupFn = (detailId) => {
  detailIdPopup.value = detailId;
  // 输入框失焦处理
  // remarkInput.value?.blur();
  showFilePopup.value = true;
  showFileData.value = shDetailVos.value.find((el) => el.detailId === detailId);
  textPopup.value = showFileData.value?.remark;
};
// 确定
const sureFilePopu = () => {
  showFilePopup.value = false;
  detailIdPopup.value = "";
};
watch(
  () => shDetailVos.value,
  (newVal) => {
    if (newVal) {
      if (detailIdPopup.value) {
        showFileData.value = newVal.find(
          (el) => el.detailId === detailIdPopup.value
        );
      }
    }
  }
);
</script>

<style scoped>
.content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  align-content: flex-start;
  background-color: #f4f4f4;
}

.orderDetail-top {
  width: 100%;
  height: 246rpx;
  display: flex;
  /* background-color: #000; */
  align-items: center;
  justify-content: flex-start;
  /* background-image: url(/images/orderDetail/bgc.png); */
  background-size: contain;
  background-repeat: no-repeat;
  box-sizing: border-box;
  padding: 0 54rpx;
}

.orderDetail-top-left {
  width: 760rpx;
  height: 104.65rpx;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  line-height: 41.86rpx;
  /* color: rgba(255, 255, 255, 1); */
  color: #000;
  font-size: 27.91rpx;
  text-align: left;
  font-family: PingFangSC-regular;
  font-weight: bold;
  margin-right: 34.88rpx;
  margin-bottom: 24rpx;
}

.orderDetail-top-status {
  width: 100%;
}

.orderDetail-top-des {
  width: 100%;
  font-weight: normal;
}

.orderDetail-top-right {
  width: 146.51rpx;
  height: 146.51rpx;
}

.up-avatarImage {
  width: 100%;
  height: 100%;
}

/* //定位 */
.orderDetail-postion {
  width: 100%;
  height: 178rpx;
  position: relative;
}

.orderDetail-wl-position {
  width: 100%;
  height: 178rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.orderDetail-address-position {
  width: 100%;
  height: 178rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.orderDetail-wl {
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  width: 702rpx;
  height: 178rpx;
  border-radius: 10px;
  background: #ffffff;
}

.orderDetail-wl-left {
  width: 52.33rpx;
  height: 52.33rpx;
  margin-right: 20rpx;
}

.orderDetail-wl-right {
  width: 580rpx;
  height: 100%;
  color: rgba(16, 16, 16, 1);
  display: flex;
  flex-wrap: wrap;
  /* font-size: 27.91rpx; */
  /* font-weight: bold; */
  text-align: left;
}

.orderDetail-wl-status {
  width: 100%;
  font-size: 32rpx;
  font-weight: bold;
}

.orderDetail-wl-mid {
  width: 100%;
  font-size: 28rpx;
}

.orderDetail-wl-btn {
  width: 100%;
  font-size: 28rpx;
  color: #666666;
}

.orderDetail-address {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  width: 702rpx;
  height: 128rpx;
  border-radius: 10px;
  opacity: 1;
  background: #ffffff;
}

.orderDetail-address-left {
  width: 52.33rpx;
  height: 52.33rpx;
  margin-right: 20rpx;
}

.orderDetail-address-center {
  width: 580rpx;
  height: 100%;
  color: rgba(16, 16, 16, 1);
  font-size: 27.91rpx;
  text-align: right;
  border-radius: 20.93rpx;
  display: flex;
  align-content: center;
  flex-wrap: wrap;
  text-align: left;
  box-sizing: border-box;
}

.orderDetail-address-address {
  width: 100%;
  line-height: 48.84rpx;
  color: rgba(155, 155, 155, 1);
  font-size: 28rpx;
  overflow: hidden;
  white-space: nowrap;
  /* 禁止换行 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}

.orderDetail-address-name {
  width: 100%;
  line-height: 48.84rpx;
  display: flex;
  justify-content: space-between;
}

.orderDetail-address-name-sure {
  font-size: 32rpx;
  font-weight: 500;
}

.orderDetail-address-name-edit {
  font-size: 24rpx;
  color: #068324;
}

.orderDetail-orderList {
  box-sizing: border-box;
  padding: 20rpx;
  width: 702rpx;
  border-radius: 10px;
  opacity: 1;
  background: #ffffff;
  margin: 20rpx auto;
}

.orderDetail-orderList-title {
  width: 100%;
  height: 40rpx;
  line-height: 40rpx;
  font-size: 27.91rpx;
  text-align: left;
  font-family: PingFangSC-regular;
  font-weight: bold;
  margin-right: 34.88rpx;
  color: #000;
}

.orderDetail-orderList-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 26.16rpx 0;
}

.orderDetail-orderList-item-img {
  width: 156rpx;
  height: 156rpx;
}

.orderDetail-orderList-item-content {
  width: 464rpx;
  height: 156rpx;
  display: flex;
  align-content: space-between;
  flex-wrap: wrap;
}

.orderDetail-orderList-item-content-title {
  width: 100%;
  max-height: 69.77rpx;
  line-height: 34.88rpx;
  display: flex;
  align-items: flex-start;
  color: rgb(16, 16, 16);
  font-size: 24.42rpx;
  font-weight: bold;
  text-align: left;
}

.mallorder-card-center-details-tag {
  display: flex;
}

.orderDetail-orderList-item-content-title .titleLv {
  width: 84rpx;
  height: 24rpx;
  border-radius: 4rpx;
  padding: 4rpx;
  background: #20b203;
  color: #fff;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 24rpx;
  margin-right: 8rpx;
  text-align: center;
  margin-right: 5rpx;
}

.orderDetail-orderList-item-content-title .titletext {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.orderDetail-orderList-item-content-weight {
  height: 34.88rpx;
  line-height: 34.88rpx;
  color: rgba(16, 16, 16, 0.59);
  font-size: 24.42rpx;
  text-align: left;
  /* 文字省略 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.orderDetail-orderList-item-content-price {
  width: 100%;
  height: 69.77rpx;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.orderDetail-orderList-item-content-price-left {
  width: 174.42rpx;
  line-height: 69.77rpx;
  height: 69.77rpx;
  color: rgba(255, 66, 66, 1);
  font-size: 18px;
}

.orderDetail-orderList-item-content-price-right {
  /* width: 139.53rpx; */
  height: 34.88rpx;
  line-height: 34.88rpx;
  color: #666666;
  font-size: 24.42rpx;
  text-align: right;
}

.orderDetail-orderList-item-allPrice {
  width: 100%;
  height: 48.84rpx;
  display: flex;
  justify-content: flex-end;
  line-height: 48.84rpx;
  font-size: 16px;
}

.orderDetail-orderList-item-text {
  color: rgba(155, 155, 155, 1);
}

.orderDetail-orderList-item-num {
  color: #ff4242;
  font-weight: bold;
}

.orderDetail-orderList-line {
  width: 711.63rpx;
  height: 5.23rpx;
  background-color: rgba(155, 155, 155, 1);
  margin: 20.93rpx 0;
}

.orderDetail-orderInfo {
  padding: 20rpx 30rpx;
  display: flex;
  flex-wrap: wrap;
  width: 702rpx;
  min-height: 380rpx;
  border-radius: 10px;
  opacity: 1;
  background: #ffffff;
  margin-top: 20rpx auto;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}

.orderDetail-orderInfo-2 {
  padding: 20rpx 30rpx;
  /* display: flex;
  flex-wrap: wrap; */
  width: 702rpx;
  /* height: 380rpx; */
  border-radius: 10px;
  opacity: 1;
  background: #ffffff;
  margin-top: 20rpx auto;
  box-sizing: border-box;
  align-items: flex-start;
  margin-bottom: 220rpx;
}

.orderDetail-orderInfo-title {
  width: 100%;
  height: 40rpx;
  line-height: 40rpx;
  font-size: 27.91rpx;
  text-align: left;
  font-weight: bold;
  margin-right: 34.88rpx;
  color: #000;
}

.orderDetail-orderInfo-item {
  box-sizing: border-box;
  width: 100%;
  height: 40rpx;
  margin: 10rpx 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.orderDetail-orderInfo-item-type {
  font-size: 28rpx;
  font-weight: normal;
  line-height: 40rpx;
  text-align: center;

  color: #9e9e9e;
}

.orderDetail-orderInfo-item-content {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: normal;
  line-height: 40rpx;
  color: #333333;
}

.copyImage {
  width: 32rpx;
  height: 32rpx;
  color: #ea5404;
}

.orderDetail-orderInfo-btn {
  box-sizing: border-box;
  width: 100vw;
  height: 142rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #fff;
  padding: 24rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  /* border-top: 1px dashed rgba(155, 155, 155, 1); */
}

/* 按钮 */
.buttom {
  width: 202.33rpx;
  height: 69.77rpx;
  line-height: 69.77rpx;
  border-radius: 17.44rpx;
  font-size: 24.42rpx;
  text-align: center;
  font-family: PingFang SC;
  box-sizing: border-box;
  margin-left: 14rpx;
}

/* 基本 */
.buttom-primary {
  background-color: #f4f4f4;
  color: rgba(0, 0, 0, 1);
}

.buttom-cancle {
  background-color: #f4f4f4;
  color: rgba(0, 0, 0, 1);
  border: 3.49rpx dotted rgba(155, 155, 155, 1);
}

/* 支付 */
.buttom-apply {
  background-color: #ffdddd;
  color: #ff3132;
}

.buttom-sure {
  background-color: #c9f6d4;
  color: #068324;
}

/* form */
.formItem {
  width: 100%;
  min-height: 140rpx;
  display: flex;
  flex-wrap: wrap;
}

.formItem .label {
  width: 100%;
  height: 44rpx;
  font-weight: normal;
  line-height: 44rpx;
  display: flex;
  align-items: center;
  font-family: PingFang SC;
  font-size: 32rpx;
  color: #171a1d;
}

.formItem .label .require {
  width: 14rpx;
  height: 14rpx;
  line-height: 14rpx;
  color: #ff5219;
  margin-right: 5rpx;
}

.formItem .input {
  width: 100%;
  height: 80rpx;
  font-weight: normal;
  line-height: 80rpx;
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #171a1d;
  position: relative;
  /* border-bottom: 1px solid #E6E6E6; */
}

.formItem .input .upload {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-content: center;
  align-items: center;
  z-index: 99;
}

.formItem .fileList {
  width: 100%;
  /* height: 200rpx; */
  display: flex;
  flex-wrap: wrap;
}

.formItem .fileItem {
  width: 140rpx;
  height: 140rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  position: relative;
}

.delImg {
  /* width: 40rpx;
  height: 40rpx; */
  position: absolute;
  right: 3rpx;
  top: 3rpx;
  display: flex;
  align-items: center;
  justify-items: flex-end;
}

.formItem .fileItem .fileItemImg {
  width: 100%;
  height: 100%;
}

/* 底部方式 */
.shfs {
  width: 100%;
  min-height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.shfsLeft {
  width: 112rpx;
  height: 40rpx;
  font-size: 28rpx;
  color: #9e9e9e;
}

.shfsAddress {
  width: 480rpx;
  text-align: right;
  position: relative;
  padding-right: 30rpx;
}

.shfsAddressT {
  /* color: #9e9e9e; */
}

.upIcon {
  width: 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 0;
}

.buttom-sub {
  width: 700rpx;
  height: 80rpx;
  border-radius: 306rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  gap: 20rpx;
  box-sizing: border-box;
  background: #068324;
  z-index: 0;
  color: #ffffff;
}

.shfsAddressB {
  text-align: right;
  /* 三行省略 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
/* 上传描述和凭证弹出层 */
.showFilePopup {
  width: 100vw;
  min-height: 860rpx;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 10px 10px 0px 0px;
  padding: 20rpx 24rpx;
}
.showFilePopup .popup_title {
  width: 100%;
  height: 50rpx;
  box-sizing: border-box;
  text-align: center;
  position: relative;
  font-family: PingFang SC;
  font-size: 36rpx;
  font-weight: 500;
  line-height: 50rpx;
  text-align: center;
  font-variation-settings: "opsz" auto;
  color: #333333;
}
.popup_title_close {
  position: absolute;
  width: 32rpx;
  height: 100%;
  right: 2rpx;
  top: 0rpx;
}
.popup_title_close_mage {
  width: 32rpx;
  height: 32rpx;
}
.showFilePopup .formItem {
  width: 100%;
  margin-top: 32rpx;
}
.showFilePopup.formItem.label {
  width: 100%;
  height: 44rpx;
  font-family: PingFang SC;
  font-size: 32rpx;
  line-height: 44rpx;
  color: #171a1d;
}
.showFilePopup .formItem .label.require {
  color: #ff5219;
}
.showFilePopup .formItem .label .number {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
}
.showFilePopup .formItem .input {
  width: 702rpx;
  height: 192rpx;
  border-radius: 20rpx;
  opacity: 1;
  background: #f8f8f8;
  z-index: 2;
  margin-top: 32rpx;
}
.showFilePopup .formItem .file {
  width: 702rpx;
  display: flex;
  flex-wrap: wrap;
  margin-top: 32rpx;
}
.showFilePopup .formItem .upload {
  width: 156rpx;
  height: 156rpx;
  margin-right: 20rpx;
}
.showFilePopup .formItem .upload .upload-img {
  width: 100%;
  height: 100%;
}
.showFilePopup .formItem .fileItem {
  width: 156rpx;
  height: 156rpx;
  margin-right: 20rpx;
}

/* //按钮 */
.popup_bottomBtn {
  width: 100%;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32rpx;
}
.popup_bottomBtn_item {
  width: 702rpx;
  height: 80rpx;
  border-radius: 306rpx;
  opacity: 1;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  gap: 20rpx;
  background: #f7f7f7;
  z-index: 0;
  box-sizing: border-box;
  margin-right: 20rpx;
}
.popup_bottomBtn_item_query {
  background: #068324;
  color: #fff;
}
/* //////////////// */
:deep(.showFilePopup .u-textarea) {
  background-color: transparent;
  overflow: auto;
}
:deep(.orderDetail-orderList-item-content-price-right .uni-input-input) {
  font-size: 22rpx;
}
</style>
