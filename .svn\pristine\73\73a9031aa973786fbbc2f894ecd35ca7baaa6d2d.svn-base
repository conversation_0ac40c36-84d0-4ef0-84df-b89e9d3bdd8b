<template name="socketDialog">
  <view class="maskBox" v-if="show" :style="{ 'margin-top': mgntop }">
    <image class="ringIcon" :src="config.imageUrl + '/shop/ringIcon.png'"></image>
    <view class="msgContent">{{ showMsgContent?.msg }}</view>
    <button class="toCheck" @click="gotoJump" type="primary">去查看</button>
  </view>
</template>

<script setup>
import { computed, reactive, ref, watch } from "vue";
import { getCurrentInstance } from "vue";
import { http } from "uview-plus";
import { onHide, onShow, onUnload } from "@dcloudio/uni-app";
import eventBus from "@/http/eventBus.js";

const { proxy } = getCurrentInstance();
const config = proxy.ipConfig;

let show = ref(false);
let mgntop = ref("0"); // 弹窗距离顶部距离
// 不清空需求登记的页面
const saveRegObjPages = reactive([
  "pages/indexView/index",
  "pagesPackages6/demandRegisterView/index",
  "pagesPackages3/myPageView/index",
  "pagesPackages6/demandRegisterView/components/marketSituationIframe",
])
// 自定义导航栏页面列表（自定义的消息top不要距离）
const customNavList = reactive([
  "pages/indexView/index",
  "pages/loginView/index",
  "pages/indexSearchListView/index",
  "pages/scanCodeTraceSouceView/index",
  "pagesPackages4/shopView/index",
  "pagesPackages4/shopView/searchPage",
  "pagesPackages3/myPageView/index",
  "pagesPackages3/shfishOrderView/index",
  "pagesPackages3/fishOrderView/index",
  "pagesPackages3/mallOrderView/index",
  "pagesPackages3/orderDetailView/index",
  "pagesPackages3/orderDetailView/other",
  "pagesPackages3/myLoanView/other",
])

let showMsgContent = reactive({
  dpId: "",
  demandId: "",
  msg: "",
  msgType: "",
  orderId: "",
});
let showTimer; // 弹窗定时器
let realtimeInterval; // 获取实时消息计时器
let blackList = ["pagesPackages6/demandShView/bargain"]; // 黑名单
let currentRoute; // 当前路由
onShow(() => {
  let token = uni.getStorageSync("user")?.token;
  if (!token) return;
  var pages = getCurrentPages();
  // 获取当前页面的路由信息
  currentRoute = pages[pages.length - 1].route;
  console.log("currentRoute", currentRoute);
  // #ifdef MP-WEIXIN
  if (customNavList.includes(currentRoute)) settingTop()
  // #endif
  getLastTime();
  // 非tabbar页清空需求登记的缓存数据
  if (!saveRegObjPages.includes(currentRoute)) {
    uni.removeStorageSync("demandRegObj")
  }
  if (blackList.includes(currentRoute)) return // 黑名单不弹消息
  getLastMsg()
  realtimeInterval = setInterval(() => {
    getRealtimeMessage();
  }, 2000);
});
onUnload(() => {
  leavePage()
});

onHide(() => {
  leavePage()
});

// 离开页面清楚定时器等
function leavePage() {
  console.log("leavePage");
  clearInterval(realtimeInterval);
  close()
}

// 获取离线消息
function getLastMsg() {
  http.get(config.baseUrl2 + "/discussPrice/offlineMessage", {}, { method: "GET" })
    .then((res) => {
      if (res.data.code == 2000 && res.data.data?.messages?.length > 0) {
        let tempMsg = res.data.data.messages[0];
        postDialogData(tempMsg)
      }
    })
}

let lastTime = ref(""); // 最后消息时间
// 获取当前用户历史最新消息时间
function getLastTime() {
  http.get(config.baseUrl2 + "/discussPrice/lastTime", {}, { method: "GET" })
    .then((res) => {
      if (res.data.code == 2000) {
        lastTime.value = res.data.data;
      }
    })
}

function getRealtimeMessage() {
  if (!lastTime.value) return;
  let postObj = {
    memberId: uni.getStorageSync('user')?.memberId,
    lastTime: lastTime.value,
  }
  http.get(proxy.ipConfig.baseUrl2 + "/discussPrice/notice", { params: postObj }, { method: "GET" })
    .then((res) => {
      if (res.data.code == 2000 && res.data.data?.messages?.length > 0) {
        let tempMsg = res.data.data.messages[0];
        postDialogData(tempMsg)
      }
    })
}

function postDialogData(tempMsg) {
  let lastCpId = uni.getStorageSync("lastCpId");
  let lastMsgTime = uni.getStorageSync("lastMsgTime");
  lastTime.value = tempMsg.time;
  if (lastCpId == tempMsg.cpId && lastMsgTime == tempMsg.time) return
  // 1:管理端分配订单(商户端) 2:管理端分配订单(养殖户端) 3:有新报价 4:商户同意新报价
  // 5:养殖户接单 6:养殖户出货 7:商户收货 8:养殖户地址修改提醒
  let shArr = [1, 3, 5, 6]
  let yzhArr = [1, 2, 3, 4, 7, 8]
  let yhlx = uni.getStorageSync("user")?.yhlx;
  if (yhlx == 2 && !shArr.includes(tempMsg.msgType)) return; // 商户
  if (yhlx == 1 && !yzhArr.includes(tempMsg.msgType)) return; // 养殖户
  uni.setStorageSync("lastCpId", tempMsg.cpId); // 存储消息
  uni.setStorageSync("lastMsgTime", tempMsg.time); // 存储消息
  show.value = true;
  eventBus.$emit('haveNewMsg'); // 有新消息，列表页监听到时刷新
  showMsgContent.dpId = tempMsg.dpId;
  showMsgContent.demandId = tempMsg.demandId;
  showMsgContent.msgType = tempMsg.msgType;
  showMsgContent.orderId = tempMsg.cpId;
  if (yhlx == 1 && tempMsg.msgType == 1) {
    tempMsg.msg = "您有新需求！"
  }
  showMsgContent.msg = tempMsg.msg;
  clearTimeout(showTimer);
  showTimer = setTimeout(() => {
    close()
  }, tempMsg.msgType == 8 ? 5000 : 10000);
}

const close = () => {
  show.value = false;
  clearTimeout(showTimer);
};

const gotoJump = async () => {
  show.value = false;
  readMsg();
  let status = await checkDemandStatus(showMsgContent.dpId)
  if (!status) {
    uni.showToast({
      title: "该需求已取消！",
      icon: "none",
      mask: true,
      duration: 2000,
    })
    setTimeout(() => {
      pager.page = 1;
      page.orderList = [];
      getPendingList();
    }, 2000);
    return
  }
  let yhlx = uni.getStorageSync("user")?.yhlx;
  if (showMsgContent.msgType == 1 && yhlx == 2) {
    // 去需求详情页
    uni.navigateTo({ url: "/pagesPackages6/demandShView/detail?demandId=" + showMsgContent.demandId });
  } else if (showMsgContent.msgType < 5) {
    // 去议价页
    uni.navigateTo({ url: `/pagesPackages6/demandShView/bargain?demandId=${showMsgContent.demandId}&dpId=${showMsgContent.dpId}` });
  } else if (showMsgContent.msgType < 8) {
    //  去订单详情页
    uni.navigateTo({
      url: "/pagesPackages3/shfishOrderView/detail?orderId=" + showMsgContent.orderId,
    });
  } else if (showMsgContent.msgType == 8) {
    //  去养殖户的收货列表
    uni.navigateTo({
      url: "/pagesPackages2/receiveAddressView/index",
    });
  }
};

function readMsg() {
  http.post(config.baseUrl2 + "/discussPrice/updateRead", { ids: [showMsgContent.orderId] }, { method: "POST" })
  http.post(config.baseUrl2 + "/shsy/cleanMsg", { xqId: showMsgContent.demandId, }, { method: "POST" });
}

function settingTop() {
  let height = uni.getMenuButtonBoundingClientRect().height
  let top = uni.getMenuButtonBoundingClientRect().top
  let statusBarHeight = uni.getSystemInfoSync().statusBarHeight
  let mgntopValue = top + height + (top - statusBarHeight) * 2 - 5
  mgntop.value = mgntopValue + "px"
}

// 校验需求单是否被取消了
function checkDemandStatus(dpId) {
  return new Promise((resolve, reject) => {
    http.get(config.baseUrl2 + "/discussPrice/cancel", { params: { dpId } }, { method: "GET" })
      .then((res) => {
        if (res.data.code == 2000) {
          resolve(true)
        } else {
          resolve(false)
        }
      })
  })
}
</script>

<style lang="scss" scoped>
.maskBox {
  position: fixed;
  left: 0;
  top: 14rpx;
  right: 0;
  bottom: 0;
  z-index: 99999;
  height: 48px;
  border-radius: 8px;
  margin: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(3, 3, 3, 0.8);
  font-family: PingFang SC;
  font-size: 32rpx;
  color: #ffffff;
  padding: 0 24rpx;

  .ringIcon {
    width: 36rpx;
    height: 36rpx;
  }

  .msgContent {
    flex: 1;
    margin: 0 12rpx;
  }

  .toCheck {
    width: 132rpx;
    height: 56rpx;
    border-radius: 56rpx;
    line-height: 56rpx;
    text-align: center;
    padding: 0;
    background: #068324;
    font-size: 28rpx;
    color: #ffffff;
    margin: 0;
  }
}
</style>
