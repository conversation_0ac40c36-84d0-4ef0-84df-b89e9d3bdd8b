<template>
  <!-- 我的协议 -->
  <view class="content">
    <!-- 顶部 -->
    <view class="navigationBar">
      <view class="navigationBarback"> </view>
      <view class="navigationBarTitleText">
        <!-- 我的 -->
      </view>
      <view class="navigationBarTitleall"> </view>
    </view>
    <!-- 帮助反馈文字说明 -->
    <view class="mainText">
      <view
        v-for="item in getPactList"
        :key="item"
        class="mainitem"
        @click="previewPdf(item.fileList, index)"
      >
        <view class="image">
          <image
            style="width: 36rpx; height: 42rpx"
            :src="config.imageUrl + '/myPage/pdf.svg'"
          >
          </image>
        </view>
        <view class="left">
          <view class="name">{{ item.pactName }}</view>
          <view class="time">{{ item.beginDate }} 至 {{ item.endDate }}</view>
        </view>
        <view class="right">
          <view class="status">
            <!-- 状态：0未开始、1正常、2到期 -->
            <span v-if="item.status == 0" class="status1">未开始</span>
            <span v-else-if="item.status == 1" class="status2">正常</span>
            <span v-else-if="item.status == 2" class="status2">到期</span>
          </view>
          <view class="arrow">
            <image
              style="width: 24rpx; height: 24rpx"
              :src="config.imageUrl + '/myPage/arrowRight.svg'"
            >
            </image>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- <ndb-foot activeName="my"></ndb-foot> -->
  <up-toast ref="uToastRef"></up-toast>
  <up-notify message="功能开发中..." ref="uNotifyRef"></up-notify>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
const uToastRef = ref(null);
const uNotifyRef = ref(null);
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";

var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl;
}
getConfig();
onMounted(() => {
  getPactListFn();
});
// 获取我的协议列表
// /grzxSh/getPactList
// 获取商户结算数据
let getPactList = ref([]);
const getPactListFn = () => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/grzxSh/getPactList",
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        getPactList.value = res.data.data;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
// PDF预览方法
const previewPdf = (urls, currentIndex) => {
  // 引用全局域名
  const baseUrl = proxy.ipConfig.baseUrl2;
  const pdfUrl = baseUrl + urls[0].sourcePath;
  const title = urls[0].curName || "";
  uni.navigateTo({
    url: `/pagesPackages3/pdfViewer/pdfViewer?url=${encodeURIComponent(
      pdfUrl
    )}&&title=${title}`,
  });
};
</script>

<style scoped>
.content {
  width: 100vw;
  /* height: 100vh; */
  display: flex;
  align-items: center;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: wrap;
  background-color: #f3f3f3;
  overflow: scroll;
  padding-bottom: 180rpx;
}

.navigationBar {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  justify-content: space-between;
  position: fixed;
  left: 0;
  top: 0;
}

.navigationBarTitleText {
  height: 100%;
  opacity: 1;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.mainText {
  width: 100%;
  text-align: center;
  line-height: 48rpx;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24rpx;
}

.mainText .mainitem {
  box-sizing: border-box;
  display: flex;
  width: 700rpx;
  height: 120rpx;
  justify-content: space-between;
  align-items: center;
  align-content: space-around;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  background-color: #fff;
  padding: 20rpx 26rpx;
}

.mainText .mainitem .image {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(252, 90, 90, 0.1);
  border-radius: 10rpx;
}

.mainText .mainitem .left {
  width: 480rpx;
  text-align: left;
  padding-left: 20rpx;
}

.mainText .mainitem .left .name {
  font-size: 28rpx;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333333;
}

.mainText .mainitem .left .time {
  font-size: 24rpx;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #999999;
}

.mainText .mainitem .right {
  width: 80rpx;
}

.mainText .mainitem .right .status {
  font-size: 24rpx;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mainText .mainitem .right .status1 {
  color: #ff9100;
}

.mainText .mainitem .right .status2 {
  color: #068324;
}

.mainText .mainitem .right .arrow {
  height: 48rpx;
  display: flex;
  justify-content: flex-end;
  align-content: center;
  align-items: center;
}
</style>
