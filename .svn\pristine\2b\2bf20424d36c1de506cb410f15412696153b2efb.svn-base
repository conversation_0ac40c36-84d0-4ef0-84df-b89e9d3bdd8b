<template>
  <view class="content">
    <!-- 顶部输入 -->
    <view class="topInput">
      <view class="topInputlabel">
        <view class="required">* </view> 商品实际出货量（斤）
      </view>
      <view class="topInputvalue">
        <input
          type="digit"
          @input="inputCheck()"
          placeholder="请输入"
          border="none"
          v-model="amount"
          style="text-align: right"
        />
      </view>
    </view>
    <!-- 检测证书 -->
    <view class="card">
      <view class="cardTitle"> <view class="required">* </view> 检测证书 </view>
      <view class="cardcontent">
        <view class="addPic">
          <image
            class="addPicpImg"
            :src="config.imageUrl + '/myPage/addPic.svg'"
            @click="afterRead(1)"
          ></image>
        </view>
        <view class="picList" v-for="(item, index) in checkList" :key="index">
          <image
            @click="previewPdf(checkList, index)"
            class="picListImg"
            :src="config.imageUrl + '/myPage/pdf.svg'"
            v-if="item.suffix.indexOf('pdf') > -1"
          ></image>
          <image
            @click="previewImage(checkList, index)"
            class="picListImg"
            :src="item.allPath"
            v-else
          ></image>
          <image
            @click="delImage(item, index, 1)"
            class="delPic"
            :src="config.imageUrl + '/myPage/del.svg'"
          ></image>
        </view>
      </view>
    </view>
    <!-- *出货凭证 -->
    <view class="card">
      <view class="cardTitle"> <view class="required">* </view> 出货凭证 </view>
      <view class="cardcontent">
        <view class="addPic">
          <image
            class="addPicpImg"
            :src="config.imageUrl + '/myPage/addPic.svg'"
            @click="afterRead(2)"
          ></image>
        </view>
        <view class="picList" v-for="(item, index) in sendList" :key="index">
          <image
            @click="previewPdf(sendList, index)"
            class="picListImg"
            :src="config.imageUrl + '/myPage/pdf.svg'"
            v-if="item.suffix.indexOf('pdf') > -1"
          ></image>
          <image
            @click="previewImage(sendList, index)"
            class="picListImg"
            :src="item.allPath"
            v-else
          ></image>
          <image
            @click="delImage(item, index, 2)"
            class="delPic"
            :src="config.imageUrl + '/myPage/del.svg'"
          ></image>
        </view>
      </view>
    </view>
    <!-- *其他材料 -->
    <view class="card">
      <view class="cardTitle"> <view class="required"> </view> 其他材料 </view>
      <view class="cardcontent">
        <view class="addPic">
          <image
            class="addPicpImg"
            :src="config.imageUrl + '/myPage/addPic.svg'"
            @click="afterRead(3)"
          ></image>
        </view>
        <view class="picList" v-for="(item, index) in elseList" :key="index">
          <image
            @click="previewPdf(elseList, index)"
            class="picListImg"
            :src="config.imageUrl + '/myPage/pdf.svg'"
            v-if="item.suffix.indexOf('pdf') > -1"
          ></image>
          <image
            @click="previewImage(elseList, index)"
            class="picListImg"
            :src="item.allPath"
            v-else
          ></image>
          <image
            @click="delImage(item, index, 3)"
            class="delPic"
            :src="config.imageUrl + '/myPage/del.svg'"
          ></image>
        </view>
      </view>
    </view>
    <!-- 底部操作按钮 -->
    <view class="operationButtons">
      <view class="operationButtonitem" @click="qrch"> 确认出货</view>
    </view>
    <!-- 确认出货 -->
  </view>
  <up-modal :show="show" :title="title" :showCancelButton="show">
    <template #default>
      <view class="modal">
        <view class="modalTitle"> 是否确认出货</view>
        <view class="modalContent">
          <view class="modalitem">
            <view class="label"> 商品出货量：</view>
            <view class="modared">{{ amount }} </view>斤
          </view>
          <view class="modalitem">
            <view class="label"> 商品成交单价：</view>
            <view class="modared">{{ orderDetailObj.lastPrice }}</view
            >元/斤
          </view>
          <view class="modalitem">
            <view class="label"> 商品成交总价：</view>
            <view class="modared">{{ totalPrice }}</view
            >元
          </view>
        </view>
      </view>
    </template>
    <template #confirmButton>
      <view class="modalBtn">
        <view class="modalBtn1" @click="show = false">取消</view>
        <view class="modalBtn2" @click="sure()">确认</view>
      </view>
    </template>
  </up-modal>
  <up-toast
    ref="uToastRef"
    z-index="999"
    :style="{
      maxWidth: '85%',
      wordBreak: 'break-all',
      overflowWrap: 'break-word',
    }"
  ></up-toast>
  <!-- 小程序上传分开处理 -->
  <up-popup
    :show="showUpload"
    @close="showUpload = false"
    bgColor="transparent"
  >
    <Wxupload
      v-model="showUpload"
      @confirm="handlewxUplaodConfirm"
      @error="handleUploadError"
      :file-types="['jpg', 'jpeg', 'png', 'pdf', 'bmp']"
      :max-size="15 * 1024 * 1024"
      :max-count="9"
    ></Wxupload>
  </up-popup>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed, nextTick } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import Wxupload from "@/components/ndb-upload-wx/index.vue";
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";
const config = proxy.ipConfig;
// 使用 ref 创建响应式数据
const show = ref(false);
const title = ref("");
const content = ref(`  <view class="modal">
    <view class="modalTitle"> 是否确认出货</view>
  </view>`);
let opId = ref("");
let orderId = ref("");
let amount = ref("");
let dpId = ref("");
let demandId = ref("");
let updateCount = ref(0);
watch(amount, () => {
  updateCount.value++;
});
function inputCheck() {
  let value;
  let maxLength = 6;
  value = amount.value;
  var params = value.toString().split(".");
  if (params.length == 0) params = ["0"];
  var n1 = params[0] * 1 + "";
  var n2 = params[1];
  // 小数只能输入两位
  if (n2 == undefined) {
    if (n1.length > maxLength) {
      n1 = n1.substring(0, maxLength);
    }
    value = n1;
  } else {
    if (n1.length > maxLength) {
      n1 = n1.substring(0, maxLength);
    }
    if (n2.length > 2) {
      n2 = n2.substring(0, 2);
    }
    value = n1 + "." + n2;
  }
  nextTick(() => {
    amount.value = value;
  });
}
let checkList = ref([]);
let sendList = ref([]);
let elseList = ref([]);
let pageType = ref(1);
onLoad((options) => {
  pageType.value = 1;
  if (options && options.orderId) {
    orderId.value = options.orderId;
    pageType.value = options.pageType;
    dpId.value = options.dpId;
    demandId.value = options.demandId;
    getOrderDetail(orderId.value);
  }
});
//小程序附件上传使用组件
const showUpload = ref(false);
const wxuploadType = ref("");
// 附件上传
function afterRead(type) {
  wxuploadType.value = type;
  // #ifdef MP-WEIXIN
  showUpload.value = true;
  // wx.chooseMessageFile({
  //   count: 9,
  //   type: "all",
  //   extension: ["jpg", "jpeg", "png", "pdf", "bmp"],
  //   success: (res) => {
  //     handleFileSelection(res.tempFiles, type);
  //   },
  //   fail: (err) => {
  //     console.error("文件选择失败:", err);
  //   },
  // });
  // #endif
  // #ifdef H5
  uni.chooseFile({
    extension: ["jpg", "jpeg", "png", "pdf", "bmp"],
    sourceType: ["album", "camera"],
    maxSize: 15 * 1024 * 1024,
    success: (res) => {
      handleFileSelection(res.tempFiles, type);
    },
    fail: (err) => {
      console.error("文件选择失败:", err);
    },
  });
  // #endif
}
// 处理文件选择结果
function handlewxUplaodConfirm(res) {
  handleFileSelection(res, wxuploadType.value);
}
function handleFileSelection(tempFiles, type) {
  const maxSize = 15 * 1024 * 1024;
  if (tempFiles.some((file) => file.size > maxSize)) {
    uni.showToast({
      title: "文件大小不能超过15MB",
      icon: "none",
    });
    return;
  }
  tempFiles.forEach((file, index) => {
    // 使用条件编译处理不同平台的滚动
    // #ifdef H5

    uni.uploadFile({
      url: proxy.ipConfig.baseUrl2 + "/common/upload",
      file: file,
      // files: res.tempFiles,
      name: "files",
      formData: {},
      header: {
        Token: uni.getStorageSync("user").token,
        // "content-type": "multipart/form-data",
      },
      success: (uploadFileRes) => {
        let obj = JSON.parse(uploadFileRes.data);
        if (obj.code === 2000) {
          if (type == 1) {
            checkList.value.push(obj.data[0]);
          } else if (type == 2) {
            sendList.value.push(obj.data[0]);
          } else if (type == 3) {
            elseList.value.push(obj.data[0]);
          }
        } else {
          // uni.showToast({
          //   title: obj.message,
          //   icon: "none",
          //   mask: true,
          //   duration: 2000,
          // });
          uToastRef.value.show({ type: "error", message: obj.message });
        }
      },
    });
    // #endif
    // #ifdef MP-WEIXIN
    uni.uploadFile({
      url: proxy.ipConfig.baseUrl2 + "/common/upload",
      // file: file,
      filePath: file.path,
      name: "files",
      formData: {
        fileNameTemp: file.name,
      },
      header: {
        Token: uni.getStorageSync("user").token,
        "content-type": "multipart/form-data",
      },
      success: (uploadFileRes) => {
        let obj = JSON.parse(uploadFileRes.data);
        if (obj.code === 2000) {
          if (type == 1) {
            checkList.value.push(obj.data[0]);
          } else if (type == 2) {
            sendList.value.push(obj.data[0]);
          } else if (type == 3) {
            elseList.value.push(obj.data[0]);
          }
        } else {
          // uni.showToast({
          //   title: obj.message,
          //   icon: "none",
          // });
          uToastRef.value.show({ type: "error", message: obj.message });
        }
      },
      fail: (err) => {},
    });
    // #endif
  });
}
//附件上传new
function afterRead2(type) {
  uni.chooseImage({
    sizeType: ["original", "compressed"], // 可以指定是原图还是压缩图，默认二者都有
    sourceType: ["album"], // 可以指定来源是相册还是相机，默认二者都有
    type: "file", // 文件类型
    extension: ["pdf"], // 限制文件扩展名为pdf
    success: (res) => {
      let filesList = [];
      res.tempFiles.forEach((item) => {
        filesList.push(filesPush(item.path, item.name ? item.name : ""));
      });
      Promise.all(filesList).then((r) => {
        let maxList = findLongestArray(r);
        switch (type) {
          case "1":
            checkList.value = maxList;
            break;
          case "2":
            sendList.value = maxList;
            break;
          case "3":
            elseList.value = maxList;
            break;
          default:
            break;
        }
      });
    },
  });
}
// 附件循环调接口

async function filesPush(url, name) {
  // #ifdef H5
  const imgBlob = await fetch(url).then((r) => r.blob());
  const imgFile = new File([imgBlob], name, {
    type: imgBlob.type,
  });
  let form = new FormData();
  form.append("avatarfile", imgFile);
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: proxy.ipConfig.baseUrl2 + "/common/upload",
      file: imgFile,
      name: "files",
      formData: {},
      header: {
        Token: uni.getStorageSync("user").token,
        // 'content-type': 'multipart/form-data'
      },
      success: (uploadFileRes) => {
        let obj = JSON.parse(uploadFileRes.data);
        if (obj.code === 200) {
          resolve(obj.data);
        }
      },
    });
  });
  // #endif
  // #ifdef MP-WEIXIN
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: proxy.ipConfig.baseUrl2 + "/common/upload",
      // filePath: res.tempFilePaths[0],
      filePath: url,
      // files:files,
      name: "files",
      formData: {},
      header: {
        Apptoken: uni.getStorageSync("token"),
        "content-type": "multipart/form-data",
      },
      success: (uploadFileRes) => {
        let obj = JSON.parse(uploadFileRes.data);
        if (obj.code === 200) {
          resolve(obj.data);
        }
      },
    });
  });

  // #endif
}

function findLongestArray(twoDArray) {
  let longest = twoDArray[0];
  for (let i = 1; i < twoDArray.length; i++) {
    if (twoDArray[i].length > longest.length) {
      longest = twoDArray[i];
    }
  }
  return longest;
}
// PDF预览方法
const previewPdf = (urls, currentIndex) => {
  const pdfUrl = urls[currentIndex].allPath;
  const title = urls[currentIndex].curName || "";
  uni.navigateTo({
    url: `/pagesPackages3/pdfViewer/pdfViewer?url=${encodeURIComponent(
      pdfUrl
    )}&&title=${title}`,
  });
};

// 图片预览方法
const previewImage = (urls, currentIndex) => {
  uni.previewImage({
    urls: urls.map((item) => item.allPath),
    current: urls[currentIndex].allPath,
  });
};
// 删除图片
const delImage = (file, index, type) => {
  uni.showModal({
    title: "删除确认",
    confirmColor: "#068324", // 确认按钮颜色
    content: "确定要删除这张凭证图片吗？",
    success: (res) => {
      if (res.confirm) {
        if (type == 1) {
          checkList.value = checkList.value.filter((_, i) => i !== index);
        } else if (type == 2) {
          sendList.value = sendList.value.filter((_, i) => i !== index);
        } else if (type == 3) {
          elseList.value = elseList.value.filter((_, i) => i !== index);
        }
        uToastRef.value.show({ type: "success", message: "删除成功" });
      }
    },
  });
};
//确认出货 - 处理数值输入验证

const uToastRef = ref(null);
const qrch = () => {
  if (amount.value == "") {
    uToastRef.value.show({
      type: "error",
      message: "请填写商品实际出货量（斤）",
    });
    return;
  }
  //限制数值,保留两位小数,最大值六位数
  if (
    !/^[0-9]+(.[0-9]{1,2})?$/.test(amount.value) ||
    Number(amount.value) > 1000000
  ) {
    uToastRef.value.show({
      type: "error",
      message: "请输入正确的数值,最大值6位数",
    });
    return;
  }
  if (checkList.value.length == 0) {
    uToastRef.value.show({ type: "error", message: "请上传检测证书" });
    return;
  }
  if (sendList.value.length == 0) {
    uToastRef.value.show({ type: "error", message: "请上传出货凭证" });
    return;
  }
  // if (elseList.value.length == 0) {
  //   uToastRef.value.show({ type: "error", message: "请上传其他材料" });
  //   return;
  // }
  show.value = true;
};
// 获取订单详情
const orderDetailObj = ref({});
const getOrderDetail = (orderId) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/farmersHome/myOrderDetail/${orderId}`,
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        orderDetailObj.value = data;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
//确认出货
const sure = () => {
  let params = {
    opId: orderDetailObj.value.opId,
    orderId: orderId.value,
    amount: amount.value,
    checkList: checkList.value,
    sendList: sendList.value,
    elseList: elseList.value,
  };
  http
    .post(proxy.ipConfig.baseUrl2 + `/farmersHome/sendOutGoods`, params, {
      method: "POST",
    })
    .then((res) => {
      if (res.data.code == 2000) {
        let postObj = {
          dpId: dpId.value,
          demandId: demandId.value,
          orderId: orderId.value,
          token: uni.getStorageSync("user")?.token,
          senderType: uni.getStorageSync("user")?.yhlx,
          status: 1,
          type: 5,
        };
        http.post(
          proxy.ipConfig.baseUrl2 + "/discussPrice/sendMessage",
          postObj,
          { method: "POST" }
        );
        // 返回上一页并且刷新
        // uni.navigateBack({
        //   delta: 1,
        // });
        if (pageType.value == 6) {
          uni.navigateBack();
        } else {
          uni.navigateTo({
            url: "/pagesPackages3/fishOrderView/index?status=1",
          });
        }

        // uni.navigateTo({
        //   url:
        //     "/pagesPackages3/fishOrderView/shipmentInformation?orderId=" +
        //     orderId.value,
        // });
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
// 成交总价计算值
const totalPrice = computed(() => {
  return (
    Number(orderDetailObj.value.lastPrice) * Number(amount.value)
  ).toFixed(2);
});
// 添加错误处理函数
const handleUploadError = (msg) => {
  // uni.showToast({
  //   title: msg,
  //   icon: "none",
  // });
  uToastRef.value.show({ type: "error", message: msg });
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  align-items: flex-start;
  align-content: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  width: 100vw;
  height: 100%;
  overflow: hidden;
  background-color: #f3f3f3;
  font-family: PingFang SC;
  //   顶部
  .topInput {
    width: 700rpx;
    height: 80rpx;
    border-radius: 20rpx;
    opacity: 1;
    /* 自动布局 */
    display: flex;
    padding: 24rpx;
    gap: 24rpx;
    background: #ffffff;
    box-sizing: border-box;
    margin-top: 24rpx;
    margin-bottom: 24rpx;
    .topInputlabel {
      flex: 3;
      display: flex;
      align-items: center;
      align-content: center;
      font-size: 28rpx;
      .required {
        color: #ff3132;
      }
    }
    .topInputvalue {
      flex: 2;
      display: flex;
      align-items: center;
      align-content: center;
    }
  }
  //检测证书
  .card {
    width: 700rpx;
    min-height: 298rpx;
    border-radius: 20rpx;
    opacity: 1;
    /* 自动布局 */
    display: flex;
    flex-wrap: wrap;
    padding: 24rpx;
    background: #ffffff;
    box-sizing: border-box;
    margin-bottom: 24rpx;
    .cardTitle {
      display: flex;
      align-items: center;
      align-content: center;
      height: 32rpx;
      font-family: PingFang SC;
      font-size: 28rpx;
      line-height: 32rpx;
      /* 检测证书 */
      color: #333333;

      .required {
        color: #ff3132;
      }
    }
    .cardcontent {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      margin-top: 24rpx;
      .addPic {
        width: 136rpx;
        height: 194rpx;
        margin-right: 36rpx;
        .addPicpImg {
          width: 100%;
          height: 100%;
        }
      }
      .picList {
        width: 136rpx;
        height: 194rpx;
        position: relative;
        margin-right: 24rpx;
        .picListImg {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          border: 1px solid #068324;
          border-radius: 2px;
        }
        .delPic {
          width: 40rpx;
          height: 40rpx;
          position: absolute;
          right: -20rpx;
          top: -20rpx;
        }
        // 最后一个
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  //操作按钮
  .operationButtons {
    /* 自动布局子元素 */
    position: fixed;
    left: 0px;
    bottom: 0px;
    width: 100%;
    height: 132rpx;
    /* 自动布局 */
    display: flex;
    justify-content: center;
    justify-content: center;
    align-items: center;
    padding: 12px;
    gap: 10px;
    box-sizing: border-box;
    background-color: #fff;
    .operationButtonitem {
      width: 700rpx;
      height: 80rpx;
      border-radius: 306rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #068324;
      font-size: 32rpx;
      color: #ffffff;
    }
  }
}
.modal {
  background-color: #fff;
  // padding: 40rpx;
  width: 100%;
  box-sizing: border-box;
  .modalTitle {
    width: 100%;
    height: 50rpx;
    font-family: PingFang SC;
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 40rpx;
    text-align: center;
  }
  .modalContent {
    width: 100%;
    justify-content: center;
    .modalitem {
      display: flex;
      justify-content: flex-start;
      height: 44rpx;
      font-family: PingFang SC;
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
      margin-bottom: 24rpx;
      .label {
        width: 50%;
        text-align: right;
      }
      .modared {
        color: #ff3132;
      }
    }
  }
}
.modalBtn {
  display: flex;
  justify-content: center;
  align-items: center;
  .modalBtn1 {
    width: 208rpx;
    height: 72rpx;
    border-radius: 306rpx;
    opacity: 1;
    /* 自动布局 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20rpx;
    gap: 20rpx;
    background: #f7f7f7;
    z-index: 0;
    box-sizing: border-box;
    font-family: PingFang SC;
    font-size: 32rpx;
    color: #333333;
    margin-right: 10rpx;
  }
  .modalBtn2 {
    width: 208rpx;
    height: 72rpx;
    border-radius: 306rpx;
    opacity: 1;
    /* 自动布局 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20rpx;
    gap: 20rpx;
    background: #068324;
    z-index: 1;
    box-sizing: border-box;
    font-family: PingFang SC;
    font-size: 32rpx;
    color: #fff;
    margin-left: 10rpx;
  }
}
</style>
