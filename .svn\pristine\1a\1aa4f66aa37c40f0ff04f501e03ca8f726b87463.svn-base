<template>
  <view class="bargainPage">
    <view class="bargainView" @click="hiddenSendPrice">
      <scroll-view :scroll-y="true" :refresher-enabled="true" :refresher-background="'#f5f5f5'"
        :refresher-triggered="scrollTriggerValue" @refresherrefresh="onScrollRefresh" :scroll-into-view="scrollIntoId"
        style="height: 100%;">
        <view style="padding-bottom:30rpx">
          <view class="contractItem" v-for="(item, index) in page.contractList" :key="index" :id="'cpId' + item.cpId">
            <view class="contract_time" v-if="item.showTime">{{ item.timeStr }}</view>
            <view class="contract_content">
              <view class="user" :style="{ opacity: item.senderType == yhlx ? 0 : 1 }">
                <image class="userAvatar" :src="item.senderType == 1 ? yzhAvatar : shAvatar"></image>
              </view>
              <view class="middleContent" :class="item.senderType == yhlx ? 'isMeContent' : ''">
                <template v-if="item.msgType == 0">
                  <view class="title">
                    买鱼需求
                  </view>
                  <view class="contentLine">商品品类：{{ item.initData?.productCategoryName }}</view>
                  <view class="contentLine">采购报价：<view class="redColor">{{ item.initData?.price }}元/斤</view>
                  </view>
                  <view class="contentLine">商品需求量：{{ item.initData?.amount }}斤</view>
                  <view class="contentLine">商品预估总价：{{ item.initData?.totalPrice }}元</view>
                  <view class="contentLine">收渔日期：{{ item.initData?.acceptTime }}</view>
                  <view class="contentLine">其他要求：{{ item.initData?.remark || '--' }}</view>
                  <audioList ref="audioListRef" :readonly="true"></audioList>
                </template>
                <template v-else-if="item.msgType == 2">
                  <view class="title" v-if="yhlx == 2"> 您已同意养殖户发出的新价格！ </view>
                  <view class="title" v-if="yhlx == 1"> 恭喜议价成功，商户已同意您发出的议价！ </view>
                </template>
                <template v-else-if="item.msgType == 3">
                  <view class="title">
                    {{ item.senderType == yhlx ? '您' : '养殖户' }}已接单！
                  </view>
                  <view class="contentLine">
                    成交单价：
                    <view class="redColor">{{ item.price }}元/斤</view>
                  </view>
                </template>
                <template v-else>
                  <view class="title">
                    发出新价格：
                    <view class="redColor">{{ item.price }}元/斤</view>
                  </view>
                  <view class="contentLine">议价理由：{{ item.msg || '--' }}</view>
                </template>
                <view class="agreeBtn" v-if="item.senderType != yhlx && (index == page.contractList.length - 1)">
                  <button v-if="yhlx == 2 && item.status == 0" @click="agreeClick(1, item)">同意新价格</button>
                  <button v-if="yhlx == 1" @click="agreeClick(2, item)">确认接单</button>
                </view>
              </view>
              <view class="user" :style="{ opacity: item.senderType == yhlx ? 1 : 0 }">
                <image class="userAvatar" :src="item.senderType == 1 ? yzhAvatar : shAvatar"></image>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <block v-if="page.contractList?.length > 0 &&
      page.contractList[page.contractList.length - 1].senderType != yhlx &&
      page.contractList[page.contractList.length - 1].msgType < 2">
      <view class="sendNewBtn" v-if="!showChangePrice">
        <button @click="showNewPriceBox">点击发送新价格</button>
      </view>
      <view class="sendNewPriceBox" v-else-if="showChangePrice">
        <view class="price">
          <image class="priceIcon" :src="config.imageUrl + '/shop/del.png'" @click="priceClick(-1)"></image>
          <view class="settingPrice">
            <view class="hideColor"> {{ newPrice }}<view class="danwei">元/斤</view>
            </view>
            <up-input border="none" fontSize="28rpx" color="#FF0000" inputAlign="center" class="priceInput" type="digit"
              v-model="newPrice" @change="inputCheck($event)"></up-input>
          </view>
          <image class="priceIcon" :src="config.imageUrl + '/shop/add.png'" @click="priceClick(1)"></image>
        </view>
        <view class="reasonAndSend">
          <up-input placeholder="请输入议价理由" v-model="reasonValue" border="none" maxlength="200"
            placeholderStyle="color:#999" :customStyle="sendInputStyle"></up-input>
          <view class="sendBtn" @click="sendNewPrice">
            发送新价格
            <image class="sendIcon" :src="config.imageUrl + '/shop/send.png'"></image>
          </view>
        </view>
      </view>
    </block>
  </view>

  <up-popup v-model:show="showAgreePopup" mode="center" :round="'20rpx'">
    <view class="agreeView">
      <view class="content" v-if="agreeType == 1">
        是否同意新价格<view class="redColor">{{ agreePrice }}元/斤</view>？
      </view>
      <view class="content" v-if="agreeType == 2">
        是否确认以<view class="redColor">{{ agreePrice }}元/斤</view>价格接单？
      </view>
      <view class="content" v-else-if="agreeType == 3">
        是否报出新价格<view class="redColor">{{ newPrice }}元/斤</view>？
      </view>
      <view class="agreeBtn">
        <button @click="cancelClick">取消</button>
        <button @click="confirmClick">确认</button>
      </view>
    </view>
  </up-popup>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed, nextTick } from "vue";
import {
  onLoad,
  onShow,
  onUnload,
} from '@dcloudio/uni-app';

// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";
const config = proxy.ipConfig
import { parseWithBigInt } from "@/http/webSocket.js";

import audioList from "@/pagesPackages6/demandRegisterView/components/audioPlayList.vue"

let dpId = ref("") // 需求单商品信息id
let demandId = ref("") // 需求单id
const page = reactive({
  contractList: [],
})

let showAgreePopup = ref(false)
let agreeType = ref(1) // 1: 同意新价格(商户) 2: 确认接单(养殖户) 3:报出新价格(发送新价格)
let agreePrice = ref("") // 同意的新价格

let showChangePrice = ref(false) // 是否编辑新价格
let newPrice = ref("0.00") // 发送的价格(默认上一次的报价价格)

let reasonValue = ref("")
const sendInputStyle = reactive({
  background: '#F0F0F0',
  height: '80rpx',
  lineHeight: '80rpx',
  padding: '0 32rpx',
  borderRadius: '20rpx',
})
const pager = reactive({
  page: 1,
  size: 100,
})
let scrollTriggerValue = ref(false) // 下拉刷新
let scrollIntoId = ref("") // 滚动到指定位置
//用户下拉动作
function onScrollRefresh() {
  scrollTriggerValue.value = true;
  setTimeout(() => {
    pager.page++
    getHistoryMessage()
  }, 500);
}
let getNowMesInterval; // 获取实时消息计时器

let yhlx = ref(1); // 1养殖户 2商户
onLoad((options) => {
  yhlx.value = uni.getStorageSync('user')?.yhlx || 1;
  console.log(options);
  dpId.value = options.dpId
  demandId.value = options.demandId
  getAvatar()
  pager.page = 1;
  getHistoryMessage()
  getNowMesInterval = setInterval(() => {
    getRealtimeMessage() // 获取实时议价消息
  }, 2000);
})

onUnload(() => {
  clearInterval(getNowMesInterval);
})

// 获取实时议价消息
function getRealtimeMessage() {
  console.log("getRealtimeMessage");
  let lastCpId = page.contractList[page.contractList.length - 1].cpId
  console.log("lastCpId", lastCpId);

  if (!lastCpId) return;
  let postObj = {
    demandId: demandId.value,
    cpId: lastCpId,
  }
  http.get(proxy.ipConfig.baseUrl2 + "/discussPrice/newest", { params: postObj }, { method: "GET" })
    .then((res) => {
      if (res.data.code == 2000 && res.data.data?.messages?.length > 0) {
        let data = res.data.data;
        page.contractList.push(...data.messages);
        nextTick(() => {
          scrollIntoId.value = "cpId" + page.contractList[page.contractList.length - 1].cpId;
          readMsgFromOther()
        })
      }
    })
}

let audioListRef = ref(null);
// 获取历史消息
function getHistoryMessage() {
  let postObj = {
    dpId: dpId.value,
    demandId: demandId.value,
    token: uni.getStorageSync('user')?.token,
    type: 0,
    senderType: yhlx.value,
    page: pager.page,
    size: pager.size,
  }
  scrollTriggerValue.value = false;
  console.log("传递的postObj", postObj);
  http.get(proxy.ipConfig.baseUrl2 + "/discussPrice/history", { params: postObj }, { method: "GET" })
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        if (data?.messages?.length > 0) {
          page.contractList.unshift(...data.messages);
        } else {
          return pager.page--
        }
        nextTick(() => {
          if (pager.page == 1) {
            scrollIntoId.value = "cpId" + page.contractList[page.contractList.length - 1].cpId;
            setTimeout(() => {
              if (page.contractList[0]?.initData?.fileList) audioListRef.value[0].setAudioList(page.contractList[0].initData.fileList)
            }, 500)
          } else {
            scrollIntoId.value = "cpId" + page.contractList[page.contractList.length - 1 - ((pager.page - 1) * pager.size)].cpId;
          }
          readMsgFromOther()
        })
      }
    })
}

let lastListLength = 0; // 上一次议价消息列表长度
// 读取其他用户发来的消息
function readMsgFromOther() {
  let tempLength = page.contractList.length;
  console.log("tempLength", tempLength);

  if (tempLength != lastListLength) {
    lastListLength = tempLength;
    let cpIds = page.contractList.filter(item => item.senderType != yhlx.value).map(item => item.cpId);
    console.log("cpIds", cpIds);
    if (cpIds.length < 1) return;
    http.post(config.baseUrl2 + "/discussPrice/updateRead", { ids: cpIds }, { method: "POST" })
  }
}

// 头像
let shAvatar = ref("")
let yzhAvatar = ref("")
function getAvatar() {
  http.get(config.baseUrl2 + "/common/getPhoto",
    { params: { dpId: dpId.value } },
    { method: "GET" }
  ).then((res) => {
    if (res.data.code == 2000) {
      shAvatar.value = res.data.data.shUrl || config.imageUrl + '/myPage/tx.svg'
      yzhAvatar.value = res.data.data.yzhUrl || config.imageUrl + '/myPage/tx.svg'
    }
  })
}

function hiddenSendPrice() {
  if (showChangePrice.value) showChangePrice.value = false
}

// 对话框里的同意按钮
function agreeClick(type, item) {
  // type: 1: 同意新价格(商户) 2: 确认接单(养殖户)
  showAgreePopup.value = true
  agreeType.value = type
  agreePrice.value = item.price
}

// 按钮打开更改价格页
function showNewPriceBox() {
  let lastPrice = ""
  if (page.contractList.length == 1) {
    lastPrice = page.contractList[0].price;
  } else {
    let myMess = page.contractList.filter(item => item.senderType == yhlx.value);
    lastPrice = myMess[myMess.length - 1].price;
  }
  newPrice.value = lastPrice;
  reasonValue.value = "";
  showChangePrice.value = true
}

// 价格加减
function priceClick(type) {
  // type: 1: 增加  -1: 减少
  let fen = newPrice.value * 100
  let tempPrice = ((fen + 10 * type) / 100).toFixed(2)
  if (tempPrice <= 0 || tempPrice.split('.')[0].length > 5) return
  newPrice.value = tempPrice
}

function inputCheck(val) {
  console.log("inputCheck");

  let value = val.split("-").join(""); // 去除-号;

  let maxLength = 5 // 最长5整+2小
  var params = value.toString().split(".");
  if (params.length == 0) params = ['0']

  var n1 = params[0] * 1 + "";
  if (params[1]) params[1] = params[1].split(".").join("");
  var n2 = params[1];

  // 小数只能输入两位
  if (n2 == undefined) {
    if (n1.length > maxLength) {
      n1 = n1.substring(0, maxLength);
    }
    value = n1
  } else {
    if (n1.length > maxLength) {
      n1 = n1.substring(0, maxLength);
    }
    if (n2.length > 2) {
      n2 = n2.substring(0, 2);
    }
    value = n1 + "." + n2;
  }
  console.log("value", value);

  setTimeout(() => {
    newPrice.value = value;
  }, 10);
}


// 有新价/理由时的 发送新价格
function sendNewPrice() {
  if (!reasonValue.value) return uni.showToast({ title: '请输入议价理由', icon: 'none' })
  if (newPrice.value <= 0) return uni.showToast({ title: '价格必须大于0！', icon: 'none' })

  showAgreePopup.value = true
  agreeType.value = 3
}

function cancelClick() {
  showAgreePopup.value = false
}
const confirmClick = async () => {
  showAgreePopup.value = false
  let status = await checkDemandStatus(dpId.value)
  if (!status) {
    uni.showToast({
      title: "该需求已取消！",
      icon: "none",
      mask: true,
      duration: 2000,
    })
    setTimeout(() => {
      uni.$emit('refreshList')
      uni.navigateBack()
    }, 2000);
    return
  }
  let postObj = {
    type: 1,
    dpId: dpId.value,
    demandId: demandId.value,
    token: uni.getStorageSync("user")?.token,
    status: 0,
    msg: reasonValue.value,
    senderType: yhlx.value
  }
  if (agreeType.value == 1) {
    // 同意新价格
    postObj.status = 1
    postObj.type = 3
    postObj.price = agreePrice.value;
    // agreePrice
  } else if (agreeType.value == 2) {
    // 确认接单
    postObj.status = 1
    postObj.type = 4
    postObj.price = agreePrice.value;
  } else if (agreeType.value == 3) {
    // 报出新价格
    postObj.price = newPrice.value;
    // newPrice
  }
  http.post(proxy.ipConfig.baseUrl2 + "/discussPrice/sendMessage", postObj, { method: "POST" })
    .then((res) => {
      if (res.data.code == 2000) {
        showChangePrice.value = false;
        reasonValue.value = "";
        uni.$emit('refreshList')
      } else {
        uni.showToast({
          title: res.data.message,
          icon: "none",
        });
      }
    })
}

// 校验需求单是否被取消了
function checkDemandStatus(dpId) {
  return new Promise((resolve, reject) => {
    http.get(config.baseUrl2 + "/discussPrice/cancel", { params: { dpId } }, { method: "GET" })
      .then((res) => {
        if (res.data.code == 2000) {
          resolve(true)
        } else {
          resolve(false)
        }
      })
  })
}
</script>

<style lang="scss" scoped>
page {
  height: 100%;
}

button:hover {
  opacity: 0.8;
}

.redColor {
  color: #FF0000;
}

.bargainPage {
  display: flex;
  flex-direction: column;
  height: 100%;
  // #ifdef MP-WEIXIN
  height: 100vh;
  // #endif
  background-color: #f5f5f5;
}

.bargainView {
  flex: 1;
  // height: 100%;
  // padding-bottom: 132rpx;
  // padding-bottom: calc(constant(safe-area-inset-bottom) + 132rpx);
  // padding-bottom: calc(env(safe-area-inset-bottom) + 132rpx);
  // box-sizing: border-box;
  overflow-y: auto;
}

.contractItem {
  padding: 0 24rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .contract_time {
    font-family: PingFang SC;
    font-size: 28rpx;
    font-weight: normal;
    color: #666666;
    text-align: center;
    padding-top: 30rpx;
  }

  .contract_content {
    display: flex;
    gap: 0 20rpx;
    margin-top: 30rpx;

    .user {
      position: relative;
      width: 76rpx;
      height: 76rpx;
      line-height: 76rpx;
      text-align: center;
      border-radius: 76rpx;
      overflow: hidden;
      font-family: PingFang SC;
      font-size: 20rpx;
      font-weight: 600;
      color: #FFFFFF;

      .userAvatar {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }

    .middleContent {
      flex: 1;
      background: #FFFFFF;
      padding: 20rpx;
      border-radius: 0 20rpx 20rpx 20rpx;
      font-family: PingFang SC;
      font-size: 28rpx;
      color: #333333;

      .title {
        display: flex;
        font-size: 32rpx;
        font-weight: 600;
      }

      .contentLine {
        display: flex;
        word-break: break-all;
        margin-bottom: 12rpx;

        .redColor {
          font-weight: 600;
        }

        &:nth-child(2) {
          margin-top: 20rpx;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .isMeContent {
      background: #F6FFF9;
      border-radius: 20rpx 0 20rpx 20rpx;
    }
  }

  .agreeBtn {
    margin-top: 20rpx;

    button {
      width: 476rpx;
      height: 72rpx;
      line-height: 72rpx;
      border-radius: 72rpx;
      background: #068324;
      font-family: PingFang SC;
      font-size: 28rpx;
      color: #FFFFFF;
    }
  }
}

.audioList {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10rpx 0;
  margin-top: 12rpx;

  .audioItem {
    display: flex;
    align-items: center;
    background: #47CB67;
    width: 194rpx;
    height: 48rpx;
    line-height: 48rpx;
    border-radius: 8rpx;
    color: #FFFFFF;
    font-size: 20rpx;
    padding-left: 12rpx;

    .playIcon {
      width: 20rpx;
      height: 20rpx;
      margin-right: 8rpx;
    }
  }
}


.sendNewBtn {
  width: 100%;
  position: sticky;
  z-index: 9;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 26rpx 24rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 26rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 26rpx);
  box-sizing: border-box;

  button {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    /* 主题色 */
    background: #fff;
    border-radius: 80rpx;
    font-size: 32rpx;
    font-family: PingFang SC;
    color: #068324;
    border: 2rpx solid #068324;
  }
}

.sendNewPriceBox {
  width: 100%;
  position: sticky;
  z-index: 9;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 26rpx 24rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 26rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 26rpx);
  box-sizing: border-box;

  .price {
    display: flex;
    align-items: center;
    padding: 12rpx 24rpx;
    border-radius: 20rpx;
    background: #F6FFF9;
    gap: 0 24rpx;

    .settingPrice {
      position: relative;
      flex: 1;
      background: #FFFFFF;
      font-family: PingFang SC;
      font-size: 28rpx;
      font-weight: 600;
      color: #FF0000;

      .hideColor {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: transparent;
        width: fit-content;
        height: 64rpx;
        line-height: 64rpx;

        .danwei {
          position: absolute;
          left: 100%;
          top: 0;
          color: #FF0000;
          width: 90rpx;
          text-align: right;
        }
      }
    }

    .priceInput {
      text-align: center;
      border-radius: 20rpx;
      height: 64rpx;
      line-height: 64rpx;
    }

    .priceIcon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .reasonAndSend {
    display: flex;
    align-items: center;
    margin-top: 24rpx;
    gap: 0 20rpx;

    .sendBtn {
      width: 196rpx;
      height: 80rpx;
      line-height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFang SC;
      font-size: 24rpx;
      color: #FFFFFF;
      background: #068324;
      gap: 0 12rpx;
      border-radius: 20rpx;

      .sendIcon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}

.agreeView {
  width: 600rpx;
  padding: 68rpx 50rpx 40rpx;
  box-sizing: border-box;

  .content {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    font-variation-settings: "opsz" auto;
    color: #333333;
    text-align: center;

    .redColor {
      color: #FF0000;
      display: inline;
      font-size: 36rpx;
    }
  }

  .agreeBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 40rpx;
    margin-top: 50rpx;

    button {
      width: 208rpx;
      height: 72rpx;
      line-height: 72rpx;
      border-radius: 72rpx;
      background: #F7F7F7;
      font-family: PingFang SC;
      font-size: 32rpx;
      color: #333333;
      margin: 0;

      &:nth-child(2) {
        background: #068324;
        color: #FFFFFF;
      }

      &:after {
        border: unset;
      }
    }
  }
}
</style>