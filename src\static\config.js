var ipConfig = {
  // // // 研发
  // baseUrl1: "http://*************:3000/sy/mini", // nodejs后端
  // baseUrl2: "http://*************:8181/scfw-mini", // java后端
  // // baseUrl2: "http://*************:8082/mini", // java后端（施润）
  // // baseUrl2: "http://*************:8080/scfw-mini", // java后端（张帆）
  // imageUrl: "http://*************:9754/sy-images", // 图片
  // pdfViewerUrl: "http://*************:9754/ndsc-pdf", // pdf组件预览地址
  // socketUrl: "ws://*************:8082/mini/ws", // socket连接地址
  // syCharts: "http://**************:7777/#/market", // echarts嵌入iframe地址

  // 测试
  // baseUrl1: "http://*************:3000/sy/mini", // nodejs后端
  // baseUrl2: "http://*************:8181/scfw-mini", // java后端
  // imageUrl: "http://*************:9754/sy-images", // 图片
  // pdfViewerUrl: "http://*************:9754/ndsc-pdf", // pdf组件预览地址
  // socketUrl: "ws://*************:8181/scfw-mini/ws", // socket连接地址
  // syCharts: "http://**************:7777/#/market", // echarts嵌入iframe地址

  // HTTPS测试
  // baseUrl1: "https://www.ndscsoft.com/sy/mini", // nodejs后端
  // baseUrl2: "https://www.ndscsoft.com/scfw-mini", // java后端
  // imageUrl: "https://www.ndscsoft.com/sy-images", // 图片
  // pdfViewerUrl: "https://www.ndscsoft.com/ndsc-pdf", // pdf组件预览地址
  // socketUrl: "wss://www.ndscsoft.com/scfw-mini/ws", // socket连接地址
  // syCharts: "https://www.ndscsoft.com/sy-h5-echarts/#/market", // echarts嵌入iframe地址

  // 生产
  baseUrl1: "https://www.wxssznj.cn/scfw-nodejs", // nodejs后端
  baseUrl2: "https://www.wxssznj.cn/scfw-mini", // java后端
  imageUrl: "https://www.wxssznj.cn/sy-images", // 图片
  pdfViewerUrl: "https://www.wxssznj.cn/cq-pdf", // pdf组件预览地址
  socketUrl: "wss://www.wxssznj.cn/scfw-mini/ws", // socket连接地址
  syCharts: "https://www.wxssznj.cn/scfw-h5-echarts/#/market", // echarts嵌入iframe地址
};

try {
  window.ipConfig = ipConfig;
} catch (error) {}

// export { ipConfig }

try {
  module.exports = { ipConfig };
} catch (error) {}
