<template>
  <!-- 结算详情 -->
  <view class="content">
    <!-- 订单信息 -->
    <view class="card">
      <view class="cardTitle">
        <view class="cardTitleborder"> </view>
        <view class="cardTitletext">订单信息 </view>
      </view>
      <view class="cardContent">
        <!-- <view class="cardContentCell">
          <view class="cardContentCellleft"> 订单编号</view>
          <view class="cardContentCellright">
            {{ orderDetailObj.orderCode }}</view
          >
        </view> -->
        <view class="cardContentCell">
          <view class="cardContentCellleft"> 商品品类</view>
          <view class="cardContentCellright">
            {{ orderDetailObj.category }}</view
          >
        </view>
        <view class="cardContentCell">
          <view class="cardContentCellleft"> 商品出货量（斤）</view>
          <view class="cardContentCellright"> {{ chDetailObj.amount }}</view>
        </view>
        <view class="cardContentCell">
          <view class="cardContentCellleft"> 商品成交单价（元/斤）</view>
          <view class="cardContentCellright"> {{ chDetailObj.price }}</view>
        </view>
        <view class="cardContentCell">
          <view class="cardContentCellleft"> 商品成交总价（元）</view>
          <view class="cardContentCellright">{{ chDetailObj.totalPrice }}</view>
        </view>
        <!-- <view class="cardContentCell">
          <view class="cardContentCellleft"> 订单完成时间</view>
          <view class="cardContentCellright">{{
            orderDetailObj.finishTime
          }}</view>
        </view> -->
        <view class="cardContentCell">
          <view class="cardContentCellleft"> 出货时间</view>
          <view class="cardContentCellright">{{ chDetailObj.sendTime }}</view>
        </view>
        <view class="cardContentCell">
          <view class="cardContentCellleft"> 出货人</view>
          <view class="cardContentCellright">{{
            chDetailObj.sendUserName
          }}</view>
        </view>
      </view>
    </view>
    <!-- 检测证书 -->
    <view class="card">
      <view class="cardTitle">
        <view class="cardTitleborder"> </view>
        <view class="cardTitletext">检测证书 </view>
      </view>
      <view class="cardContent cardContentBtoom">
        <view
          class="picList"
          v-for="(item, index) in chDetailObj?.jczsList"
          :key="index"
        >
          <image
            @click="previewPdf(item)"
            class="picListImg"
            :src="config.imageUrl + '/myPage/pdf.svg'"
            v-if="item.suffix.indexOf('pdf') > -1"
          ></image>
          <image
            class="picListImg"
            :src="item.sourcePath"
            @click="previewFile(item.sourcePath, 'jczsList', index)"
            v-else
          ></image>
        </view>
        <nbdNodata
          v-if="chDetailObj?.jczsList?.length == 0"
          :imgStyleObj="imgStyleObj"
          :nodataTextTitle="nodataTextTitle"
        ></nbdNodata>
      </view>
    </view>
    <!-- 出货凭证 -->
    <view class="card">
      <view class="cardTitle">
        <view class="cardTitleborder"> </view>
        <view class="cardTitletext">出货凭证 </view>
      </view>
      <view class="cardContent cardContentBtoom">
        <view
          class="picList"
          v-for="(item, index) in chDetailObj?.chpzList"
          :key="index"
        >
          <image
            @click="previewPdf(item)"
            class="picListImg"
            :src="config.imageUrl + '/myPage/pdf.svg'"
            v-if="item.suffix.indexOf('pdf') > -1"
          ></image>
          <image
            class="picListImg"
            :src="item.sourcePath"
            @click="previewFile(item.sourcePath, 'chpzList', index)"
            v-else
          ></image>
        </view>
        <nbdNodata
          v-if="chDetailObj?.chpzList?.length == 0"
          :imgStyleObj="imgStyleObj"
          :nodataTextTitle="nodataTextTitle"
        ></nbdNodata>
      </view>
    </view>
    <!-- 其他材料 -->
    <view class="card">
      <view class="cardTitle">
        <view class="cardTitleborder"> </view>
        <view class="cardTitletext">其他材料 </view>
      </view>
      <view class="cardContent cardContentBtoom">
        <view
          class="picList"
          v-for="(item, index) in chDetailObj?.qtclList"
          :key="index"
        >
          <image
            @click="previewPdf(item)"
            class="picListImg"
            :src="config.imageUrl + '/myPage/pdf.svg'"
            v-if="item.suffix.indexOf('pdf') > -1"
          ></image>
          <image
            class="picListImg"
            :src="item.sourcePath"
            @click="previewFile(item.sourcePath, 'qtclList', index)"
            v-else
          ></image>
        </view>
        <nbdNodata
          v-if="chDetailObj?.qtclList?.length == 0"
          :imgStyleObj="imgStyleObj"
          :nodataTextTitle="nodataTextTitle"
        ></nbdNodata>
      </view>
    </view>
  </view>
  <up-toast ref="uToastRef" :z-index="9999"></up-toast>
  <up-notify message="功能开发中..." ref="uNotifyRef"></up-notify>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, reactive, onMounted, watch, computed } from "vue";
// 导入ipConfig
import { getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
const { proxy } = getCurrentInstance();
const route = useRoute();
// 导入http
import { http, toast } from "uview-plus";
import nbdNodata from "@/components/ndb-no-data/index.vue";
const uToastRef = ref(null);
const uNotifyRef = ref(null);
var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl1;
}
getConfig();
//缺省图
let nodataTextTitle = ref("暂无数据");
let imgStyleObj = ref({
  width: "200rpx",
  height: "200rpx",
});
onMounted(() => {});
// 新增onShow生命周期
onShow(() => {});
onLoad((options) => {
  if (options && options.orderId) {
    getOrderDetail(options.orderId);
    getchDetail(options.orderId);
  }
});
// 获取订单详情
const orderDetailObj = ref({});
const getOrderDetail = (orderId) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/farmersHome/myOrderDetail/${orderId}`,
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        orderDetailObj.value = data;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};

const previewFile = (url, listType, index) => {
  if (url.toLowerCase().endsWith(".pdf")) {
    uni.navigateTo({
      url: `/pagesPackages3/pdfViewer/pdfViewer?url=${encodeURIComponent(url)}`,
    });
  } else {
    const list = chDetailObj.value[listType] || [];
    const urls = list.map((item) => item.sourcePath);
    uni.previewImage({
      urls: urls,
      current: index,
      loop: true,
    });
  }
};
const previewPdf = (urls) => {
  const pdfUrl = urls.sourcePath;
  const title = urls.curName || "";
  uni.navigateTo({
    url: `/pagesPackages3/pdfViewer/pdfViewer?url=${encodeURIComponent(
      pdfUrl
    )}&&title=${title}`,
  });
};
const chDetailObj = ref({});
const getchDetail = (orderId) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/farmersHome/sendOutGoodsDetails/${orderId}`,
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        chDetailObj.value = data;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
</script>

<style scoped lang="scss">
.content {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  width: 100vw;
  // height: 100%;
  overflow: hidden;
  background-color: #f3f3f3;
  font-family: PingFang SC;
  padding-bottom: 24rpx;
  .card {
    width: 700rpx;
    background-color: #fff;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    padding: 0 12px;
    background: #ffffff;
    box-sizing: border-box;
    margin-top: 24rpx;
    .cardTitle {
      width: 100%;
      height: 80rpx;
      display: flex;
      align-content: center;
      align-items: center;
      .cardTitleborder {
        width: 6rpx;
        height: 24rpx;
        border-radius: 60rpx;
        opacity: 1;
        background: #068324;
      }
      .cardTitletext {
        height: 32rpx;
        opacity: 1;
        font-family: PingFang SC;
        font-size: 28rpx;
        font-weight: 600;
        line-height: 32rpx;
        letter-spacing: 0px;
        color: #333333;
        margin-left: 8rpx;
      }
    }
    .cardContent {
      .cardContentCell {
        width: 100%;
        height: 80rpx;
        border-bottom: 2rpx solid #f3f3f3;
        display: flex;
        justify-content: space-between;
        align-content: center;
        align-items: center;
        .cardContentCellleft {
          font-family: PingFang SC;
          font-size: 28rpx;
          color: #666666;
        }
        .cardContentCellright {
          font-family: PingFang SC;
          font-size: 28rpx;
          color: #333333;
        }
      }
      .picList {
        width: 136rpx;
        height: 194rpx;
        position: relative;
        margin-right: 24rpx;
        margin-bottom: 12rpx;
        .picListImg {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          border: 1px solid #068324;
          border-radius: 2px;
        }
        .delPic {
          width: 40rpx;
          height: 40rpx;
          position: absolute;
          right: -20rpx;
          top: -20rpx;
        }
        // 最后一个
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .cardContentBtoom {
      padding-bottom: 24rpx;
      display: flex;
      flex-wrap: wrap;
    }
  }
}
</style>
