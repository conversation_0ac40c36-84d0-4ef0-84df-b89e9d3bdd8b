<template>
  <view
    class="content"
    v-if="orderDetail != null"
    :style="`padding-top:${titleHeight}px`"
  >
    <!-- 顶部 -->
    <view
      class="navigationBar"
      :style="`height:${searchBarHeight}px;top:${titleHeight}px`"
    >
      <view class="navigationBarback" @click="goTomallorder()">
        <up-icon name="arrow-left" size="42rpx" color="#000000"></up-icon>
      </view>
      <view class="navigationBarTitleText"> 订单详情 </view>
      <view class="navigationBarTitleall"> </view>
    </view>

    <view
      class="orderDetail-top"
      :style="`background-image:url(${config.imageUrl}/orderDetail/bgc.png);margin-top:${searchBarHeight}px`"
    >
      <view class="orderDetail-top-left">
        <view class="orderDetail-top-status">
          <!-- 待收货 -->
          <span>卖家已发货</span>
        </view>
        <view
          class="orderDetail-top-des"
          @click.stop="trackShipment(orderDetail?.orderId)"
        >
          <span style="color: #068324">查看物流详情 </span>
          <up-icon name="arrow-right" size="12" color=" #068324"></up-icon>
          <!-- <span>
            还剩{{ convertTime(remainingTime, orderId) }}自动确认收货</span
          > -->
        </view>
      </view>
      <!-- <view class="orderDetail-top-right">
        <image class="up-avatarImage" src="/images/orderDetail/car.png">
        </image>
      </view> -->
    </view>
    <view class="orderDetail-postion">
      <!-- <view class="orderDetail-wl-position">
        <view class="orderDetail-wl">
          <view class="orderDetail-wl-left">
            <image
              class="up-avatarImage"
              :src="config.imageUrl + '/orderDetail/car.svg'"
            >
            </image>
          </view>
          <view class="orderDetail-wl-right">
            <view class="orderDetail-wl-status"> 待收货 </view>
            <view class="orderDetail-wl-mid"> 官方上门包裹已由家门口签收 </view>
            <view class="orderDetail-wl-btn">
              {{ orderDetail?.shTime }}
            </view>
          </view>
        </view>
      </view> -->
      <view class="orderDetail-address-position">
        <view class="orderDetail-address">
          <view class="orderDetail-address-left">
            <image
              class="up-avatarImage"
              :src="config.imageUrl + '/orderDetail/address.svg'"
            >
            </image>
          </view>
          <view class="orderDetail-address-center">
            <view class="orderDetail-address-name">
              <view class="orderDetail-address-name-sure">
                <text>{{ orderDetail?.dzglVo.name }}</text> &nbsp;
                <text>{{ orderDetail?.dzglVo.phone }}</text>
              </view>
              <view
                v-if="orderDetail?.status == 0 || orderDetail?.status == 1"
                @click.stop="gotoEditadd()"
                class="orderDetail-address-name-edit"
              >
                修改地址
              </view>
            </view>
            <view class="orderDetail-address-address">
              {{ orderDetail?.dzglVo.province }}{{ orderDetail?.dzglVo.city
              }}{{ orderDetail?.dzglVo.district }},
              {{ orderDetail?.dzglVo.detailAddress }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="orderDetail-orderList">
      <view class="orderDetail-orderList-title"> 订单商品 </view>
      <view
        v-for="(items, index) in orderDetail?.detailVos"
        :key="index"
        class="orderDetail-orderList-item"
      >
        <view class="orderDetail-orderList-item-img">
          <image class="up-avatarImage" :src="items.productVo.pic"> </image>
        </view>
        <view class="orderDetail-orderList-item-content">
          <view class="titletext"> {{ items.productVo.name }} </view>
          <view class="mallorder-card-center-details-tag">
            <text
              v-for="(tag, index) in items.productVo.tagVos"
              class="titleLv"
              :style="`backgroundColor: ${tag.color}`"
            >
              {{ tag.name }}
            </text>
          </view>
          <view class="orderDetail-orderList-item-content-weight">
            规格: {{ items.productVo.spData }}
          </view>
          <view class="orderDetail-orderList-item-content-price">
            <view class="orderDetail-orderList-item-content-price-left">
              ¥{{ items.amount }}
            </view>
            <view class="orderDetail-orderList-item-content-price-right">
              ×{{ items.nums }}
            </view>
          </view>
          <!-- 退款或售后 -->
          <view
            class="orderDetail-orderList-tkorsh"
            v-if="items.productVo.sfth == 1"
            @click="afterSalesDetails(orderDetail?.orderId)"
          >
            <view class="orderDetail-orderList-tkorsh-text">
              <span v-if="items.productVo.shStatue == 1">已申请退款</span>
              <span v-if="items.productVo.shStatue == 2"
                >退款完成:退款金额￥{{ items.productVo.thAmount }}</span
              >
              <span v-if="items.productVo.shStatue == 3">退款关闭</span>
            </view>
            <view class="upIcon">
              <up-icon name="arrow-right" size="14"></up-icon>
            </view>
          </view>
        </view>
      </view>
      <view class="orderDetail-orderList-item-allPrice">
        <view class="orderDetail-orderList-item-text">
          共{{ orderDetail?.ddspsl }}件商品,合计
        </view>
        <view class="orderDetail-orderList-item-num">
          ￥{{ orderDetail?.amount }}
        </view>
      </view>
    </view>
    <!-- <view class="orderDetail-orderList-line">
    </view> -->
    <view class="orderDetail-orderInfo">
      <view class="orderDetail-orderInfo-title"> 订单信息 </view>
      <view class="orderDetail-orderInfo-item">
        <view class="orderDetail-orderInfo-item-type"> 订单编号 </view>
        <view class="orderDetail-orderInfo-item-content">
          <!-- {{ orderDetail?.orderNo }} -->
          <span v-if="orderDetail?.sfcd == 0">{{ orderDetail?.orderNo }}</span>
          <span v-if="orderDetail?.sfcd == 1">
            <span
              v-if="
                orderDetail?.status == 0 ||
                (orderDetail?.status == 51 && orderDetail?.cancelType == 1) ||
                (orderDetail?.status == 51 && orderDetail?.cancelType == 4)
              "
              >{{ orderDetail?.orderNo }}</span
            >
            <span v-else>
              {{ orderDetail?.orderNoChild }}
            </span>
          </span>
          <image
            @click.stop="
              copy(
                orderDetail?.sfcd == 0
                  ? orderDetail?.orderNo
                  : orderDetail?.orderNoChild
              )
            "
            class="copyImage"
            :src="config.imageUrl + '/orderDetail/copy.svg'"
          >
          </image>
        </view>
      </view>
      <view
        class="orderDetail-orderInfo-item"
        v-if="orderDetail?.status != 0 && orderDetail?.sfqx != 1"
      >
        <view class="orderDetail-orderInfo-item-type"> 支付方式 </view>
        <view class="orderDetail-orderInfo-item-content">
          <!-- 1微信支付 2额度支付-邮储银行定向支付  -->
          <span v-if="orderDetail?.type == 1">微信支付</span>
          <span v-if="orderDetail?.type == 2">额度支付-邮储银行定向支付</span>
          <span v-if="orderDetail?.type == 3">银联支付</span>
          <span v-if="orderDetail?.type == 99">待支付</span>
        </view>
      </view>
      <view class="orderDetail-orderInfo-item">
        <view class="orderDetail-orderInfo-item-type"> 下单时间 </view>
        <view class="orderDetail-orderInfo-item-content">
          {{ orderDetail?.xdTime }}
        </view>
      </view>
      <view
        v-if="orderDetail?.status != 1 && orderDetail?.status != 51"
        class="orderDetail-orderInfo-item"
      >
        <view class="orderDetail-orderInfo-item-type"> 付款时间 </view>
        <view class="orderDetail-orderInfo-item-content">
          {{ orderDetail?.fkTime }}
        </view>
      </view>
      <view
        v-if="
          orderDetail?.status != 1 &&
          orderDetail?.status != 0 &&
          orderDetail?.status != 51
        "
        class="orderDetail-orderInfo-item"
      >
        <view class="orderDetail-orderInfo-item-type"> 发货时间 </view>
        <view class="orderDetail-orderInfo-item-content">
          {{ orderDetail?.fhTime }}
        </view>
      </view>
    </view>

    <view class="orderDetail-orderInfo-btn">
      <view
        v-if="
          orderDetail?.status == 2 &&
          orderDetail?.sfsqtk == 0 &&
          orderDetail?.sqtkStatue != 3
        "
        @click.stop="refundApplication(orderDetail, 'refund')"
        class="buttom buttom-primary"
      >
        申请退款
      </view>
      <view
        v-if="
          orderDetail?.status == 2 &&
          orderDetail?.sfsqtk == 0 &&
          orderDetail?.sqtkStatue == 3
        "
        @click.stop="recallAfterSales2(orderDetail?.orderId, false)"
        class="buttom buttom-primary"
      >
        退款详情
      </view>
      <view
        v-if="
          orderDetail?.status == 2 &&
          orderDetail?.sfsqtk == 1 &&
          orderDetail?.sqtkJd == 3
        "
        @click.stop="recallAfterSales2(orderDetail?.orderId, false)"
        class="buttom buttom-primary"
      >
        退款详情
      </view>
      <view
        v-if="orderDetail?.sfsqtk == 1 && orderDetail?.sqtkStatue == 1"
        @click.stop="recallAfterSales2(orderDetail?.orderId, true)"
        class="buttom buttom-primary"
      >
        退款详情
      </view>
      <view
        v-if="
          orderDetail?.status == 2 &&
          orderDetail?.sfsqtk == 1 &&
          orderDetail?.sqtkJd != 3
        "
        class="mallorder-card-top-apply"
        @click.stop="recallAfterSales3(orderDetail?.orderId, false)"
      >
        <view class="buttom buttom-primary"> 退款详情 </view>
      </view>
      <!-- <view
        v-if="orderDetail?.sfsqtk == 1 && orderDetail?.sqtkStatue != 1"
        @click.stop="recallAfterSales2(orderDetail?.orderId, false)"
        class="buttom buttom-primary"
      >
        退款详情
      </view> -->
      <view
        v-if="
          orderDetail?.status == 51 &&
          orderDetail?.sfsqtk == 1 &&
          orderDetail?.sqtkJd != 3
        "
        class="mallorder-card-top-apply"
        @click.stop="recallAfterSales3(orderDetail?.orderId, false)"
      >
        <view class="buttom buttom-primary"> 退款详情 </view>
      </view>
      <view
        @click.stop="trackShipment(orderDetail?.orderId)"
        class="buttom buttom-primary"
      >
        查看物流
      </view>
      <view @click.stop="confirmReceipt(orderId)" class="buttom buttom-sure">
        确认收货
      </view>
    </view>
  </view>
  <up-loading-page :loading="orderDetail == null"></up-loading-page>
  <up-toast ref="uToastRef"></up-toast>
  <up-popup :z-index="99" :show="showPopup" @close="showPopup = false">
    <view class="popupBox">
      <view class="popup_title"> 选择退款售后商品 </view>
      <view class="popup_type">
        <view class="popup_type_title"> 请选择售后类型 </view>
        <view class="popup_spec">
          <view
            class="popup_scaleItem"
            v-for="(item, index) in thArr"
            :key="index"
            :class="currentScaleIndex === index ? 'activeScale' : ''"
            @click="scaleClick(item, index)"
          >
            {{ item.name }}
          </view>
        </view>
      </view>
      <view class="popup_main">
        <view class="popup_type_title"> 请选择商品 </view>
        <view class="popup_spec popup_spec-2">
          <view
            v-for="(items, index) in shDetailVos"
            :key="index"
            class="orderDetail-orderList-item"
          >
            <up-checkbox-group
              @change="changeItem(items)"
              checked
              class="checkbox"
            >
              <up-checkbox
                :value="items?.detailId"
                :checked="items?.checked"
                shape="circle"
                activeColor="#068324"
                iconSize="14"
              />
            </up-checkbox-group>
            <view class="orderDetail-orderList-item-img">
              <image class="up-avatarImage" :src="items.productVo.pic"> </image>
            </view>
            <view class="orderDetail-orderList-item-content">
              <view class="orderDetail-orderList-item-content-title">
                <view class="titletext"> {{ items.productVo.name }} </view>
                <view class="mallorder-card-center-details-tag">
                  <text
                    v-for="(tag, index) in items.productVo.tagVos"
                    class="titleLv"
                    :style="`backgroundColor: ${tag.color}`"
                  >
                    {{ tag.name }}
                  </text>
                </view>
              </view>
              <view class="orderDetail-orderList-item-content-weight">
                规格: {{ items.productVo.spData }}
              </view>
              <view class="orderDetail-orderList-item-content-price">
                <view class="orderDetail-orderList-item-content-price-left">
                  ¥{{ items.amount }}
                </view>
                <view class="orderDetail-orderList-item-content-price-right">
                  <!-- ×{{ items.nums }} -->
                  <up-number-box
                    v-if="refundOrAfterSale == 'refund'"
                    v-model="items.itemCount"
                    :disabled="true"
                  >
                  </up-number-box>
                  <up-number-box
                    v-if="refundOrAfterSale == 'aftersale'"
                    v-model="items.itemCount"
                    :min="0"
                    :max="items.nums"
                  >
                  </up-number-box>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="afterSalesInfo" @click="gotoShsm">
        售后说明 &nbsp;&nbsp;&nbsp;
        <up-icon name="arrow-right" size="12"></up-icon>
      </view>
      <view class="popup_bottomBtn">
        <button class="normalButton" @click="next()">下一步</button>
      </view>
    </view>
  </up-popup>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import { onLoad, onBackPress } from "@dcloudio/uni-app";
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
// 导入http
import { http, toast } from "uview-plus";
const uToastRef = ref(null);
var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl1;
}

//详细信息
let info = ref([
  {
    name: "订单编号",
    value: "2023025623452223",
  },
  {
    name: "支付方式",
    value: "额度支付-邮储银行定向支付",
  },
  {
    name: "下单时间",
    value: "2025-03-04 10:17:23",
  },
  {
    name: "付款时间",
    value: "2025-03-04 10:18:35",
  },
  {
    name: "发货时间",
    value: "2025-03-04 18:26:43",
  },
]);

// 复制
const copy = (text) => {
  // 使用条件编译处理不同平台的复制
  // #ifdef H5
  const tempInput = document.createElement("textarea");
  tempInput.value = text;
  tempInput.style.position = "fixed";
  tempInput.style.opacity = 0;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand("copy");
  tempInput.remove();
  uToastRef.value.show({
    type: "success",
    title: "成功主题",
    message: "复制成功",
  });
  // #endif
  // #ifdef MP-WEIXIN
  wx.setClipboardData({
    data: text, // 或者你想要复制的内容
    success: function () {
      uToastRef.value.show({
        type: "success",
        title: "成功主题",
        message: "复制成功",
      });
    },
  });
  // #endif
};
// 修改地址
const gotoEditadd = () => {
  uni.navigateTo({
    url: `/pagesPackages2/receiveAddressView/index?from=orderDetail&url=/pagesPackages3/orderDetailView/index&orderId=${orderId.value}`,
  });
};
// 确认取消
const gn = () => {
  uToastRef.value.show({
    type: "error",
    icon: false,
    title: "失败主题",
    message: "功能正在开发中",
    iconUrl: "https://uview-plus.jiangruyi.com/resources/toast/error.png",
  });
};

getConfig();
// 获取微信右上角胶囊高度
let titleHeight = ref(0);
let searchBarHeight = ref(44);
const getHeight = () => {
  // #ifdef H5
  // #endif
  // #ifdef MP-WEIXIN
  let res = wx.getMenuButtonBoundingClientRect();
  titleHeight.value = res.top; //获取距离赋值给
  searchBarHeight.value = res.height;
  // #endif
};
// 加载
let orderId = ref("");
let orderDetail = ref(null);
onLoad((options) => {
  orderId.value = options.orderId;
  fetchOrderDetails();
  getOrderWaitPayTime();
  getHeight();
});
// 获取订单详情
const fetchOrderDetails = () => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/store/order/find",
      {
        orderId: orderId.value,
      },
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        orderDetail.value = data;
        if (uni.getStorageSync("addressData")) {
          orderDetail.value.dzglVo = {
            ...orderDetail.value.dzglVo,
            ...JSON.parse(uni.getStorageSync("addressData")),
          };
          uni.removeStorageSync("addressData");
          upDateCartAddress(orderDetail.value.dzglVo);
          return;
        }
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
//修改订单收货地址
const upDateCartAddress = (item) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/store/order/dzgl/update",
      {
        ...item,
      },
      {
        method: "POST",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        // uToastRef.value.show({ type: "success", message: res.data.message });
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
//取消订单
const cancelOrder = (orderId) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/store/order/cancel/${orderId}`,
      {
        orderId: orderId,
      },
      {
        method: "POST",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        uToastRef.value.show({
          type: "success",
          title: "成功主题",
          message: "订单取消成功",
        });
        getmallOrderList();
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
// 确认收货
const confirmReceipt = (orderId) => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/store/order/sure/receive/${orderId}`,
      {
        orderId: orderId,
      },
      {
        method: "POST",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        uToastRef.value.show({
          type: "success",
          title: "成功主题",
          message: "操作成功",
        });
        // 返回订单页
        uni.navigateTo({
          url: `/pagesPackages3/mallOrderView/index`,
        });
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
//自动收货剩余时间
// 待付款剩余时间
let remainingTime = ref(0);
let timer = setInterval(() => {
  remainingTime.value--;
  if (remainingTime.value <= 0) {
    clearInterval(timer);
  }
}, 1000);
//查询订单待支付时间
const getOrderWaitPayTime = () => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + `/store/order/wait/pay/time`,
      {
        orderId: orderId.value,
      },
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        const currentDate = getValidDate(data.currentTime);
        const xdDate = getValidDate(data.xdTime);
        if (currentDate && xdDate) {
          remainingTime.value =
            (xdDate.getTime() +
              60 * 60 * 24 * 7 * 1000 -
              currentDate.getTime()) /
            1000;
        } else {
          remainingTime.value = 0;
        }
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
const getValidDate = (dateString) => {
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
};
// 修改时间转换方法
const convertTime = (seconds, orderId) => {
  if (seconds == 1) {
    // 自动收货
    confirmReceipt(orderId);
  }
  const days = Math.floor(seconds / (3600 * 24));
  seconds %= 3600 * 24;
  const hours = Math.floor(seconds / 3600);
  seconds %= 3600;
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;

  let result = "";
  if (days > 0) {
    result += `${days}天`;
  }
  if (hours > 0) {
    result += `${hours}时`;
  }
  if (minutes > 0 || (days === 0 && hours === 0)) {
    result += `${minutes}分`;
  }
  result += `${secs}秒`;

  return result;
};
// 申请退款
// 售后方式 1仅退款 2退货退款
let thArr = ref([
  {
    type: 1,
    name: "仅退款",
  },
  {
    type: 2,
    name: "退货退款",
  },
]);
let currentScaleIndex = ref(0);
let shDetailVos = ref([]);
let shDzglVo = ref(null);
let categoryId = ref(""); //商品种类id
let refundOrAfterSale = ref("");
const refundApplication = (item, type) => {
  refundOrAfterSale.value = type;
  if (type == "refund") {
    currentScaleIndex.value = 0;
    thArr.value = [
      // {
      //   type: 1,
      //   name: "仅退款",
      // },
      {
        type: 2,
        name: "退货退款",
      },
    ];
    shDetailVos.value = item.detailVos.map((item) => ({
      ...item,
      checked: true,
      itemCount: item.nums,
    }));
    if (item.shStatue != null) {
      uToastRef.value.show({
        type: "error",
        title: "",
        message: "已申请过退款,不支持再次申请",
      });
      return;
    }
  }
  if (type == "aftersale") {
    currentScaleIndex.value = 0;
    thArr.value = [
      {
        type: 2,
        name: "退货退款",
      },
    ];
    shDetailVos.value = item.detailVos.map((item) => ({
      ...item,
      checked: false,
      itemCount: 0,
    }));
  }
  orderId.value = item.orderId;
  shDzglVo.value = item.dzglVo;
  categoryId.value = item?.detailVos[0]?.productVo?.categoryId;

  showPopup.value = true;
};
const changeItem = (item) => {
  shDetailVos.value.forEach((el) => {
    if (item.detailId == el.detailId) {
      el.checked = !item.checked;
    }
  });
};
//退款弹出层
const showPopup = ref(false);
let refundOrAfterSaleText = ref("退货退款");
const scaleClick = (item, index) => {
  refundOrAfterSaleText.value = item.name;
  currentScaleIndex.value = index;
};
//退款下一步
const next = () => {
  let tkparams = {
    orderId: orderId.value,
    type: thArr.value[currentScaleIndex.value].type,
    shDetailVos: shDetailVos.value.filter((el) => {
      if (el.checked && el.itemCount > 0) {
        return el;
      }
    }),
    shDzglVo: shDzglVo.value,
  };
  uni.setStorageSync("tkparams", tkparams);
  if (tkparams.shDetailVos.length == 0) {
    return uToastRef.value.show({
      type: "error",
      title: "失败主题",
      message: "请选择商品且商品数量不能为0",
    });
  }
  if (refundOrAfterSaleText.value == "仅退款") {
    // 仅退款
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/refundOnly?orderId=${orderId.value}&refundOrAfterSale=${refundOrAfterSale.value}`,
    });
  }
  if (refundOrAfterSaleText.value == "退货退款") {
    // 退款退货
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/index?orderId=${orderId.value}&refundOrAfterSale=${refundOrAfterSale.value}`,
    });
  }
  showPopup.value = false;
};
//撤回售后
// const recallAfterSales2 = (orderId, showBtn) => {
//   uni.navigateTo({
//     url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTk?orderId=${orderId}&currentIndex=${3}&showBtn=${showBtn}`,
//   });
// };
//撤回售后
const recallAfterSales2 = (orderId, showBtn) => {
  if (showBtn) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTk?orderId=${orderId}&currentIndex=3&showBtn=${showBtn}`,
    });
  } else {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTked?orderId=${orderId}&currentIndex=3&showBtn=${showBtn}`,
    });
  }
};
// 售后详情
const afterSalesDetails = (orderId) => {
  // 售后向
  if (
    orderDetail.value.sfsqsh == 1 &&
    (orderDetail.value.sqshStatue == 2 ||
      orderDetail.value.sqshStatue == 3 ||
      orderDetail.value.sqshStatue == 4)
  ) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesCompleted?orderId=${orderId}`,
    });
    return;
  }
  if (orderDetail.value.status == 3 && orderDetail.value.sqshStatue == 3) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesCompleted?orderId=${orderId}&currentIndex=3&showBtn=false`,
    });
    return;
  }

  if (orderDetail.value.status == 3 && orderDetail.value.sqshStatue == 1) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetails?orderId=${orderId}&currentIndex=${4}&showBtn=false`,
    });
    return;
  }
  if (orderDetail.value.status == 1 && orderDetail.value.sqshStatue == 1) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetails?orderId=${orderId}&currentIndex=3&showBtn=true`,
    });
    return;
  }
  // 退款向
  if (orderDetail.value.status == 71 && orderDetail.value.sfsqtk == 1) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTk?orderId=${orderId}&currentIndex=3&showBtn=true`,
    });
    return;
  }
  if (orderDetail.value.status == 70 && orderDetail.value.sfsqtk == 1) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTk?orderId=${orderId}&currentIndex=3&showBtn=true`,
    });
    return;
  }
  if (orderDetail.value.status == 3 && orderDetail.value.sqtkStatue == 3) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTked?orderId=${orderId}&currentIndex=3&showBtn=false`,
    });
    return;
  }
  if (
    orderDetail.value.sfsqsh != 1 &&
    orderDetail.value.sfsqtk == 1 &&
    orderDetail.value.sqtkJd != 3
  ) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTk?orderId=${orderId}&currentIndex=3&showBtn=false`,
    });
    return;
  }
  if (
    orderDetail.value.sfsqsh != 1 &&
    orderDetail.value.sfsqtk == 1 &&
    orderDetail.value.sqtkJd == 3
  ) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTked?orderId=${orderId}&currentIndex=3&showBtn=false`,
    });
    return;
  }
  if (orderDetail.value.sfsqtk == 1 && orderDetail.value.sqtkStatue == 3) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTked?orderId=${orderId}&currentIndex=3&showBtn=false`,
    });
    return;
  }
  if (
    orderDetail.value.status == 2 &&
    orderDetail.value.sfsqtk == 0 &&
    orderDetail.value.sqtkStatue == 3 &&
    orderDetail.value.sfsqsh != 1
  ) {
    uni.navigateTo({
      url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTked?orderId=${orderId}&currentIndex=3&showBtn=false`,
    });
    return;
  }
  uni.navigateTo({
    url: `/pagesPackages3/refundAndReturn/afterSalesCompleted?orderId=${orderId}`,
  });
};
const recallAfterSales3 = (orderId, showBtn) => {
  uni.navigateTo({
    url: `/pagesPackages3/refundAndReturn/afterSalesDetailsTk?orderId=${orderId}&currentIndex=3&showBtn=false`,
  });
};
// 跳转售后说明
const gotoShsm = () => {
  uni.navigateTo({
    url: `/pagesPackages3/helpAndfeedbackView/supportGuidelines?categoryId=${categoryId.value}`,
  });
};
// 查看物流
const trackShipment = (orderId) => {
  uni.navigateTo({
    url: `/pagesPackages3/mallOrderView/viewLogistics?orderId=${orderId}`,
  });
};
// new------------------------
// onBackPress(() => {
//   // 返回的默认页面
//   uni.reLaunch({ url: "/pagesPackages3/mallOrderView/index" }); // 关闭所有页面并打开目标页
//   return true; // 阻止默认返回行为
// });
const goTomallorder = () => {
  // 返回的商店页面
  uni.navigateTo({ url: "/pagesPackages3/mallOrderView/index" }); // 关闭所有页面并打开目标页
  return;
};
</script>

<style scoped>
#app {
  background-color: #f4f4f4;
}

.content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  align-content: flex-start;
  background-color: #f4f4f4;
}

.orderDetail-top {
  width: 100%;
  height: 246rpx;
  display: flex;
  /* background-color: #000; */
  align-items: center;
  justify-content: flex-start;
  /* background-image: url(/images/orderDetail/bgc.png); */
  background-size: contain;
  background-repeat: no-repeat;
  box-sizing: border-box;
  padding: 0 54rpx;
}

.orderDetail-top-left {
  width: 760rpx;
  height: 104.65rpx;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  line-height: 41.86rpx;
  /* color: rgba(255, 255, 255, 1); */
  color: #000;
  font-size: 27.91rpx;
  text-align: left;
  font-family: PingFangSC-regular;
  font-weight: bold;
  margin-right: 34.88rpx;
  margin-bottom: 24rpx;
}

.orderDetail-top-status {
  width: 100%;
  font-size: 36rpx;
  font-weight: bold;
}

.orderDetail-top-des {
  width: 100%;
  font-weight: normal;
  font-size: 28rpx;
  display: flex;
}

.orderDetail-top-right {
  width: 146.51rpx;
  height: 146.51rpx;
}

.up-avatarImage {
  width: 100%;
  height: 100%;
}

/* //定位 */
.orderDetail-postion {
  width: 100%;
  /* height: 270rpx; */
  height: 92rpx;
  position: relative;
}

.orderDetail-wl-position {
  width: 100%;
  height: 178rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: -50rpx;
}

.orderDetail-address-position {
  width: 100%;
  /* height: 178rpx; */
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  /* top: 145rpx; */
  top: -40rpx;
}

.orderDetail-wl {
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  width: 702rpx;
  height: 178rpx;
  border-radius: 10px;
  background: #ffffff;
}

.orderDetail-wl-left {
  width: 52.33rpx;
  height: 52.33rpx;
  margin-right: 20rpx;
}

.orderDetail-wl-right {
  width: 580rpx;
  height: 100%;
  color: rgba(16, 16, 16, 1);
  display: flex;
  flex-wrap: wrap;
  /* font-size: 27.91rpx; */
  /* font-weight: bold; */
  text-align: left;
}

.orderDetail-wl-status {
  width: 100%;
  font-size: 32rpx;
  font-weight: bold;
}

.orderDetail-wl-mid {
  width: 100%;
  font-size: 28rpx;
}

.orderDetail-wl-btn {
  width: 100%;
  font-size: 28rpx;
  color: #666666;
}

.orderDetail-address {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  width: 702rpx;
  height: 128rpx;
  border-radius: 10px;
  opacity: 1;
  background: #ffffff;
}

.orderDetail-address-left {
  width: 52.33rpx;
  height: 52.33rpx;
  margin-right: 20rpx;
}

.orderDetail-address-center {
  width: 580rpx;
  height: 100%;
  color: rgba(16, 16, 16, 1);
  font-size: 27.91rpx;
  text-align: right;
  border-radius: 20.93rpx;
  display: flex;
  align-content: center;
  flex-wrap: wrap;
  text-align: left;
  box-sizing: border-box;
}

.orderDetail-address-address {
  width: 100%;
  line-height: 48.84rpx;
  color: rgba(155, 155, 155, 1);
  font-size: 28rpx;
  overflow: hidden;
  white-space: nowrap;
  /* 禁止换行 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}

.orderDetail-address-name {
  width: 100%;
  line-height: 48.84rpx;
  display: flex;
  justify-content: space-between;
}

.orderDetail-address-name-sure {
  font-size: 32rpx;
  font-weight: 500;
}

.orderDetail-address-name-edit {
  font-size: 24rpx;
  color: #068324;
}

.orderDetail-orderList {
  box-sizing: border-box;
  padding: 20rpx;
  width: 702rpx;
  border-radius: 10px;
  opacity: 1;
  background: #ffffff;
  margin: 20rpx auto;
}

.orderDetail-orderList-title {
  width: 100%;
  height: 40rpx;
  line-height: 40rpx;
  font-size: 27.91rpx;
  text-align: left;
  font-family: PingFangSC-regular;
  font-weight: bold;
  margin-right: 34.88rpx;
  color: #000;
}

.orderDetail-orderList-item {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  /* flex-wrap: wrap; */
  margin: 48rpx 0;
}

.orderDetail-orderList-item-img {
  width: 156rpx;
  height: 156rpx;
  border-radius: 6rpx;
  overflow: hidden;
}

.orderDetail-orderList-item-content {
  width: 484rpx;
  height: 156rpx;
  display: flex;
  align-content: space-between;
  flex-wrap: wrap;
  padding-right: 20rpx;
  box-sizing: border-box;
}

.orderDetail-orderList-item-content-title {
  width: 100%;
  max-height: 69.77rpx;
  line-height: 34.88rpx;
  display: flex;
  align-items: flex-start;
  color: rgb(16, 16, 16);
  font-size: 24.42rpx;
  font-weight: bold;
  text-align: left;
}

.mallorder-card-center-details-tag {
  display: flex;
}

.titleLv {
  width: 84rpx;
  height: 24rpx;
  border-radius: 4rpx;
  padding: 4rpx;
  background: #20b203;
  color: #fff;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 24rpx;
  margin-right: 8rpx;
  text-align: center;
  margin-right: 5rpx;
}

.orderDetail-orderList-item-content-title .titletext {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.titletext {
  width: 100%;
  height: 20px;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 500;
  line-height: normal;
  font-variation-settings: "opsz" auto;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.orderDetail-orderList-item-content-weight {
  height: 34.88rpx;
  line-height: 34.88rpx;
  color: rgba(16, 16, 16, 0.59);
  font-size: 24.42rpx;
  text-align: left;
  /* 省略 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.orderDetail-orderList-item-content-price {
  width: 100%;
  height: 40rpx;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.orderDetail-orderList-item-content-price-left {
  width: 174.42rpx;
  line-height: 40rpx;
  height: 40rpx;
  color: rgba(255, 66, 66, 1);
  font-size: 18px;
}

.orderDetail-orderList-item-content-price-right {
  /* width: 139.53rpx; */
  height: 34.88rpx;
  line-height: 34.88rpx;
  color: #666666;
  font-size: 24.42rpx;
  text-align: right;
}

.orderDetail-orderList-item-allPrice {
  width: 100%;
  height: 48.84rpx;
  display: flex;
  justify-content: flex-end;
  line-height: 48.84rpx;
  font-size: 16px;
}

.orderDetail-orderList-item-text {
  color: rgba(155, 155, 155, 1);
}

.orderDetail-orderList-item-num {
  color: #ff4242;
  font-weight: bold;
}

.orderDetail-orderList-line {
  width: 711.63rpx;
  height: 5.23rpx;
  background-color: rgba(155, 155, 155, 1);
  margin: 20.93rpx 0;
}

.orderDetail-orderInfo {
  padding: 20rpx 30rpx;
  display: flex;
  flex-wrap: wrap;
  width: 702rpx;
  height: 380rpx;
  border-radius: 10px;
  opacity: 1;
  background: #ffffff;
  margin-top: 20rpx auto;
  box-sizing: border-box;
  margin-bottom: 220rpx;
}

.orderDetail-orderInfo-title {
  width: 100%;
  height: 40rpx;
  line-height: 40rpx;
  font-size: 27.91rpx;
  text-align: left;
  font-weight: bold;
  margin-right: 34.88rpx;
  color: #000;
}

.orderDetail-orderInfo-item {
  box-sizing: border-box;
  width: 100%;
  height: 40rpx;
  margin: 10rpx 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.orderDetail-orderInfo-item-type {
  font-size: 28rpx;
  font-weight: normal;
  line-height: 40rpx;
  text-align: center;

  color: #9e9e9e;
}

.orderDetail-orderInfo-item-content {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: normal;
  line-height: 40rpx;
  color: #333333;
}

.copyImage {
  width: 32rpx;
  height: 32rpx;
  color: #ea5404;
}

.orderDetail-orderInfo-btn {
  box-sizing: border-box;
  width: 100vw;
  height: 142rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #fff;
  padding: 24rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  /* border-top: 1px dashed rgba(155, 155, 155, 1); */
}

/* 按钮 */
.buttom {
  width: 202.33rpx;
  height: 69.77rpx;
  line-height: 69.77rpx;
  border-radius: 17.44rpx;
  font-size: 24.42rpx;
  text-align: center;
  font-family: PingFang SC;
  box-sizing: border-box;
  margin-left: 14rpx;
}

/* 基本 */
.buttom-primary {
  background-color: #f4f4f4;
  color: rgba(0, 0, 0, 1);
}

.buttom-cancle {
  background-color: #f4f4f4;
  color: rgba(0, 0, 0, 1);
  border: 3.49rpx dotted rgba(155, 155, 155, 1);
}

/* 支付 */
.buttom-apply {
  background-color: #ffdddd;
  color: #ff3132;
}

.buttom-sure {
  background-color: #c9f6d4;
  color: #068324;
}

/* 申请退款 */
.popupBox {
  position: relative;
  padding: 40rpx 24rpx 0;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  background-color: #f5f5f5;
  max-width: 100vw;
  box-sizing: border-box;
}

.popup_title {
  width: 100%;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #333333;
}

.popup_type {
  width: 100%;
  height: 174rpx;
  border-radius: 10px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 20rpx 24rpx;
  background: #ffffff;
  box-sizing: border-box;
  margin-top: 32rpx;
}

.popup_main {
  border-radius: 10px;
  padding: 20rpx 24rpx 40rpx;
  background: #ffffff;
  box-sizing: border-box;
  margin-top: 24rpx;
  max-height: 60vh;
  overflow: auto;
}

.popup_type_title {
  width: 100%;
  height: 50rpx;
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.popup_spec {
  width: 100%;
  display: flex;
  margin-top: 20rpx;
}

.popup_spec-2 {
  flex-wrap: wrap;
}

.popup_scaleItem {
  min-width: 160rpx;
  height: 64rpx;
  border-radius: 192rpx;
  opacity: 1;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #f4f4f4;
  z-index: 1;
  color: #333333;
  margin-right: 24rpx;
  box-sizing: border-box;
}

.activeScale {
  background: rgba(255, 49, 50, 0.08);
  border: 1px solid rgba(255, 49, 50, 0.5);
  color: #ff3132;
}

.orderDetail-orderList-item {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  /* flex-wrap: wrap; */
  /* margin: 10rpx 0; */
}

.orderDetail-orderList-item-img {
  width: 156rpx;
  height: 156rpx;
  margin-right: 20rpx;
}

.orderDetail-orderList-item-content-title {
  width: 100%;
  max-height: 69.77rpx;
  line-height: 34.88rpx;
  display: flex;
  align-items: flex-start;
  color: rgb(16, 16, 16);
  font-size: 24.42rpx;
  font-weight: bold;
  text-align: left;
}

.mallorder-card-center-details-tag {
  display: flex;
  width: 100%;
}

.orderDetail-orderList-item-content-title .titleLv {
  width: 84rpx;
  height: 24rpx;
  border-radius: 4rpx;
  padding: 4rpx;
  background: #20b203;
  color: #fff;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 24rpx;
  margin-right: 8rpx;
  text-align: center;
  margin-right: 5rpx;
}

.orderDetail-orderList-item-content-title .titletext {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.orderDetail-orderList-item-content-weight {
  height: 34.88rpx;
  line-height: 34.88rpx;
  color: rgba(16, 16, 16, 0.59);
  font-size: 24.42rpx;
  text-align: left;
}

.orderDetail-orderList-item-content-price {
  width: 100%;
  height: 40rpx;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.orderDetail-orderList-item-content-price-left {
  width: 174.42rpx;
  line-height: 40rpx;
  height: 40rpx;
  color: rgba(255, 66, 66, 1);
  font-size: 18px;
}

.orderDetail-orderList-item-content-price-right {
  /* width: 139.53rpx; */
  height: 34.88rpx;
  line-height: 34.88rpx;
  color: #666666;
  font-size: 24.42rpx;
  text-align: right;
}

.orderDetail-orderList-item-allPrice {
  width: 100%;
  height: 48.84rpx;
  display: flex;
  justify-content: flex-end;
  line-height: 48.84rpx;
  font-size: 16px;
}

.orderDetail-orderList-item-text {
  color: rgba(155, 155, 155, 1);
}

.orderDetail-orderList-item-num {
  color: #ff4242;
  font-weight: bold;
}

.afterSalesInfo {
  text-align: center;
  width: 100%;
  height: 50rpx;
  line-height: 50rpx;
  margin-bottom: 140rpx;
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-content: center;
  justify-content: center;
  margin-top: 20rpx;
}

.popup_bottomBtn {
  width: 100%;
  display: flex;
  justify-content: center;
}

.normalButton {
  width: 700rpx;
  height: 80rpx;
  border-radius: 306rpx;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  gap: 20rpx;
  box-sizing: border-box;
  background: #068324;
  z-index: 0;
  color: #ffffff;
}

.up-toast {
  z-index: 9999 !important;
}
:deep(.orderDetail-orderList-item-content-price-right .uni-input-input) {
  font-size: 22rpx;
}
/* 商品退款或售后 */
.orderDetail-orderList-tkorsh {
  width: 100%;
  height: 40rpx;
  color: #666;
  line-height: 40rpx;
  background-color: #dadbde;
  border-radius: 4rpx;
  padding: 0 12rpx;
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
}
.orderDetail-orderList-tkorsh-text {
  width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/* //new */
.navigationBar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  position: fixed;
  left: 0;
  top: 0px;
  padding: 14rpx 6rpx;
  box-sizing: border-box;
}
.navigationBarback {
  width: 52rpx;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  z-index: 10;
}
.navigationBarTitleText {
  width: 100%;
  height: 100%;
  opacity: 1;
  font-size: 32rpx;
  /* font-weight: bold; */
  color: #000000;
  position: absolute;
  top: 0;
  left: 0;
  text-align: center;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  background-color: #fff;
}
</style>
