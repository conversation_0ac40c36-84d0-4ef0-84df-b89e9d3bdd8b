<template>
  <!-- 无数据 -->
  <view class="nodataBox">
    <view class="nodata">
      <view class="nodataImg">
        <image
          :style="imgStyleObj"
          class="picListImg"
          :src="config.imageUrl + '/myPage/noData.svg'"
        ></image
      ></view>
      <view class="nodataTextTitle">{{ nodataTextTitle }}</view>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, reactive, onMounted, watch, computed } from "vue";

// 导入ipConfig
import { getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
const { proxy } = getCurrentInstance();
const route = useRoute();
// 导入http
import { http, toast } from "uview-plus";
const uToastRef = ref(null);
const uNotifyRef = ref(null);
var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl1;
}
const props = defineProps({
  imgStyleObj: {
    type: Object, // 定义类型为对象
    default: () => ({}), // 默认值为空对象
  },
  nodataTextTitle: {
    type: String,
    default: "暂无数据",
  },
});
getConfig();
onMounted(() => {});
onLoad((options) => {});
// 新增onShow生命周期
onShow(() => {});
</script>

<style scoped lang="scss">
.nodataBox {
  padding: 12px;
  box-sizing: border-box;
  margin: 0 auto;
  .nodata {
    padding: 40rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 10px;
    .nodataImg {
      width: 100%;
      display: flex;
      justify-content: center;
    }
    .nodataTextTitle {
      display: flex;
      width: 100%;
      justify-content: center;
      text-align: center;
      height: 32rpx;
      opacity: 1;
      font-family: PingFang SC;
      font-size: 24rpx;
      font-weight: normal;
      line-height: 32rpx;
      color: #666666;
    }
  }
}
</style>
