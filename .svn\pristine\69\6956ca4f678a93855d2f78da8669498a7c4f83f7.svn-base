<template>
	<view class="content">
		<!-- 		<view class="background-box"></view>
		<up-navbar :bgColor="'transparent'" title="详情" @rightClick="rightClick" :autoBack="true">
		</up-navbar> -->

		<view class="tab-content-wrap">
			<!-- 加载中状态 -->
			<view v-if="loading" class="loading-box">
				<text>加载中...</text>
			</view>

			<!-- 内容区域 -->
			<view v-else class="tab-content">
				<view class="main-text-box">
					<view class="title">
						{{ articleData.title }}
					</view>
					<!-- 原class名：text,bug单：70069要求和下面的统一 -->
					<view class="rich-content">
						{{ articleData.summary }}
					</view>

					<!-- 动态显示图片 -->
					<image v-if="articleData.coverImage" class="main-img" :src="articleData.coverImage" mode=""></image>

					<!-- 动态显示内容，移除itemclick事件 -->
					<rich-text :nodes="processedContent" class="rich-content"></rich-text>

					<view class="bot-box">
						<view class="icon-box" @click="toggleLike">
							<up-icon name="thumb-up" v-if="!articleData.liked"></up-icon>
							<up-icon name="thumb-up-fill" color="#FF7B00" v-else></up-icon>
							点赞
						</view>
						<view class="icon-box" @click="toggleFavorite">
							<up-icon name="star" v-if="!articleData.favorited"></up-icon>
							<up-icon name="star-fill" color="#FF7B00" v-else></up-icon>
							收藏
						</view>
						<view class="icon-box">
							<up-icon name="eye-fill"></up-icon>
							{{ articleData.viewCount }}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { getCurrentInstance } from "vue";
import { http, toast } from "uview-plus";
import { useTokenPolling } from "../../pages/indexView/components/useTokenPolling.js";
import { eventBus, emitArticleStatusChanged } from "../../pages/indexView/components/useEventBus.js";

const { proxy } = getCurrentInstance();
const config = proxy.ipConfig;
const { token } = useTokenPolling();

const bg = ref('linear-gradient(0deg, #7DDB9F -69%, #F5FFF9 185%), #FFFFFF')
const articleId = ref('');
const articleData = ref(null);
const loading = ref(true);

// 加载文章数据
async function loadArticleData() {
	try {
		loading.value = true;
		console.log('[详情页] 加载文章数据，ID:', articleId.value);
		const res = await http.get(proxy.ipConfig.baseUrl2 + "/knowledge/details", {
			params: {
				knowledgeId: articleId.value
			}
		});

		if (res.data.data) {
			articleData.value = res.data.data;
			// 存储文章ID，确保使用正确的ID进行点赞/收藏操作
			articleData.value.knowledgeId = articleId.value;
			console.log('[详情页] 文章数据加载成功:', {
				title: articleData.value.title,
				id: articleId.value,
				liked: articleData.value.liked,
				favorited: articleData.value.favorited
			});
		} else {
			toast({ title: '文章不存在', icon: 'none' });
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	} catch (error) {
		console.error('[详情页] 加载文章数据失败:', error);
		toast({ title: typeof error === 'string' ? error : '加载失败，请稍后重试', icon: 'none' });
		setTimeout(() => {
			uni.navigateBack();
		}, 1500);
	} finally {
		loading.value = false;
	}
}

onLoad((option) => {
	articleId.value = option.id || '';
	// 记录全局文章ID，用于状态同步
	// 使用条件编译处理不同平台
	// #ifdef H5
	if (typeof window !== 'undefined') {
		window._currentArticleId = articleId.value;
	}
	// #endif
	
	// 使用uni-app的存储API，兼容所有平台
	try {
		uni.setStorageSync('current_article_id', articleId.value);
	} catch (err) {
		console.error('[详情页] 保存文章ID失败:', err);
	}
	
	if (articleId.value) {
		loadArticleData();
	} else {
		toast({ title: '参数错误', icon: 'none' });
		setTimeout(() => {
			uni.navigateBack();
		}, 1500);
	}
});

// 点赞功能
async function toggleLike() {
	if (!token.value) {
		uni.showToast({
			title: '请登录后操作',
			icon: 'none'
		});
		return;
	}

	try {
		const url = articleData.value.liked ? '/knowledge/cancelLike' : '/knowledge/like';
		await http.post(proxy.ipConfig.baseUrl2 + url + '?knowledgeId=' + articleId.value, {
			// knowledgeId: articleId.value
		});

		articleData.value.liked = !articleData.value.liked;
		
		// 使用统一的事件发送方法
		console.log('发送点赞状态变更事件，文章ID:', articleId.value);
		try {
			// 发送多种可能的ID格式，确保首页能匹配上
			const eventData = {
				id: articleId.value,
				klgId: articleId.value,
				knowledgeId: articleId.value,
				liked: articleData.value.liked,
				type: 'like',
				// 添加更多信息帮助调试
				title: articleData.value.title || '',
				timestamp: Date.now()
			};
			console.log('[详情页] 发送事件数据:', eventData);
			emitArticleStatusChanged(eventData);
			console.log('点赞事件发送成功');
		} catch (eventError) {
			console.error('点赞事件发送失败:', eventError);
		}
		
		uni.showToast({
			title: articleData.value.liked ? '点赞成功' : '已取消点赞',
			icon: 'none'
		});
	} catch (error) {
		console.error('点赞API调用失败:', error);
		uni.showToast({
			title: typeof error === 'string' ? error : '操作失败，请稍后重试',
			icon: 'none'
		});
	}
}

// 收藏功能
async function toggleFavorite() {
	if (!token.value) {
		uni.showToast({
			title: '请登录后操作',
			icon: 'none'
		});
		return;
	}

	try {
		const url = articleData.value.favorited ? '/knowledge/cancelCollect' : '/knowledge/collect';
		await http.post(proxy.ipConfig.baseUrl2 + url + '?knowledgeId=' + articleId.value, {
			// knowledgeId: articleId.value
		});

		articleData.value.favorited = !articleData.value.favorited;
		
		// 使用统一的事件发送方法
		console.log('发送收藏状态变更事件，文章ID:', articleId.value);
		try {
			// 发送多种可能的ID格式，确保首页能匹配上
			const eventData = {
				id: articleId.value,
				klgId: articleId.value,
				knowledgeId: articleId.value,
				favorited: articleData.value.favorited,
				type: 'favorite',
				// 添加更多信息帮助调试
				title: articleData.value.title || '',
				timestamp: Date.now()
			};
			console.log('[详情页] 发送事件数据:', eventData);
			emitArticleStatusChanged(eventData);
			console.log('收藏事件发送成功');
		} catch (eventError) {
			console.error('收藏事件发送失败:', eventError);
		}
		
		uni.showToast({
			title: articleData.value.favorited ? '收藏成功' : '已取消收藏',
			icon: 'none'
		});
	} catch (error) {
		console.error('收藏API调用失败:', error);
		uni.showToast({
			title: typeof error === 'string' ? error : '操作失败，请稍后重试',
			icon: 'none'
		});
	}
}

// 处理富文本内容，使图片自适应屏幕宽度
const processedContent = computed(() => {
	if (!articleData.value || !articleData.value.content) return '';
	
	// 处理富文本内容中的图片
	// #ifdef MP-WEIXIN
	return processImageInContentWeixin(articleData.value.content);
	// #endif
	
	// #ifndef MP-WEIXIN
	return processImageInContent(articleData.value.content);
	// #endif
});

// 处理富文本内容中的图片，使其自适应（标准版本）
function processImageInContent(html) {
	if (!html) return '';
	
	// 替换图片标签，添加样式使其自适应
	return html.replace(/<img[^>]*>/gi, match => {
		// 保留原有属性
		if (match.indexOf('style=') !== -1) {
			// 如果已有style属性，在其中添加宽度设置
			return match.replace(/style="([^"]*)"/i, 'style="$1;width:100%;height:auto;border-radius:14rpx;margin:20rpx 0;display:block;"');
		} else {
			// 如果没有style属性，添加新的style属性
			return match.replace(/<img/i, '<img style="width:100%;height:auto;border-radius:14rpx;margin:20rpx 0;display:block;"');
		}
	});
}

// 微信小程序特定处理：图片样式优化
function processImageInContentWeixin(html) {
	if (!html) return '';
	
	// 微信小程序中，不使用border-radius，改用外层包裹实现圆角
	return html.replace(/<img[^>]*>/gi, match => {
		// 提取src属性
		const srcMatch = match.match(/src=["']([^"']*)["']/i);
		const src = srcMatch ? srcMatch[1] : '';
		
		// 为微信小程序创建包裹结构
		if (src) {
			// 使用外层div包裹，为每个图片创建单独的容器
			return `<div class="mp-image-wrapper"><img src="${src}" style="width:100%;height:auto;margin:0;display:block;" /></div>`;
		}
		
		// 如果没有src，则返回原始标签
		return match;
	});
}
</script>

<style scoped lang="scss">
.content {
	// padding-bottom: 170rpx;
	height: 100vh;
	overflow: hidden;
	box-sizing: border-box;
}

.main-custom-head-box {
	position: relative;

	.title {
		position: absolute;
		left: 50%;
		bottom: 27rpx;
		transform: translateX(-50%);
		font-size: 36rpx;
		color: #333333;
		font-weight: 550;
	}
}

.background-box {
	width: 100%;
	height: 44px;
	left: 0rpx;
	top: 0rpx;
	background-size: cover;
	background: linear-gradient(0deg, #7DDB9F -69%, #F5FFF9 185%), #FFFFFF;
}

.tab-content-wrap {
	background: #F4F4F4;
	padding: 24rpx;
	height: 100%;
	overflow: auto;
	padding-bottom: 100rpx;
	box-sizing: border-box;
}

.loading-box {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	font-size: 28rpx;
	color: #666;
}

.main-text-box {
	padding: 0 24rpx;
}

.tab-content {
	background: #FFF;
	padding: 24rpx 0;

	.main-img {
		width: 100%;
		height: 360rpx;
		border-radius: 14rpx;
		margin: 20rpx 0;
	}

	.title {
		font-size: 32rpx;
		color: #333333;
		line-height: 1.5;
		font-weight: 600;
		text-align: center;
	}

	.text {
		color: #666666;
		font-size: 24rpx;
		line-height: 1.5;
		margin-bottom: 20rpx;
	}

	.bot-box {
		display: flex;
		justify-content: flex-end;
		color: #666666;
		font-size: 24rpx;
		align-items: center;
		gap: 20rpx;
		margin-top: 30rpx;
	}

	.icon-box {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 5rpx;
		cursor: pointer;
	}

	// 添加富文本内容样式
	:deep(.rich-content) {
		width: 100%;
		overflow: hidden;
		
		img {
			width: 100% !important;
			height: auto !important;
			border-radius: 14rpx !important;
			margin: 20rpx 0 !important;
			display: block !important;
		}
		
		// 微信小程序图片包裹器样式
		.mp-image-wrapper {
			overflow: hidden;
			border-radius: 14rpx;
			margin: 20rpx 0;
			width: 100%;
		}
	}
}
</style>