<template>
  <view
    class="wrap"
    :style="'background-image:url(' + config.imageUrl + '/login/login-bg.png)'"
  >
    <up-navbar
      title="登录"
      @rightClick="rightClick"
      :autoBack="true"
      bgColor="transparent"
    >
    </up-navbar>
    <view class="logo-box">
      <image
        class="logo"
        :src="config.imageUrl + '/login/logo.png'"
        mode="aspectFit"
      ></image>
      <view class=""> 欢迎登录苏渔小程序 </view>
    </view>
    <view class="form-box">
      <view class="input-box">
        <image
          :src="config.imageUrl + '/login/user-icon.png'"
          mode="aspectFit"
          class="icon"
        ></image>
        <input
          type="text"
          placeholder="请输入账号"
          class="inner"
          v-model.trim="name"
        />
      </view>
      <view class="input-box">
        <image
          :src="config.imageUrl + '/login/pwd-icon.png'"
          mode="aspectFit"
          class="icon"
        ></image>
        <input
          :password="showConfirmPassword"
          type="text"
          placeholder="请输入密码"
          class="inner inner2 uni-input-input"
          v-model.trim="password"
          autocomplete="new-password" 
        />
        <view class="eyes" @click="showConfirmPassword = !showConfirmPassword">
          <image
            class="eye"
            v-if="showConfirmPassword"
            :src="config.imageUrl + '/login/eye-close.png'"
            mode="aspectFit"
          ></image>
          <image
            class="eye"
            v-else
            :src="config.imageUrl + '/login/eye-icon.png'"
            mode="aspectFit"
          ></image>
        </view>
      </view>
      <view class="submit-box">
        <button :disabled="!submitStatus" @click="submitHandle">确定</button>
      </view>
    </view>
    <view class="tip-box">
      <image
        :src="config.imageUrl + '/login/warning-icon.png'"
        mode="aspectFit"
        class="tip-icon"
      ></image>
      <view class="">
        养殖户端账户密码由管理员统一分配，若需要开户请联系管理人员。
      </view>
    </view>
  </view>
  <view
    style="
      color: #999;
      font-size: 24rpx;
      position: fixed;
      bottom: 30rpx;
      left: 50%;
      transform: translateX(-50%);
    "
    v-if="version"
  >
    当前版本号：V{{ version }}
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import {
  onLoad,
  onShow,
  onReachBottom,
  onPullDownRefresh,
} from "@dcloudio/uni-app";
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
// 导入http
import { http, toast } from "uview-plus";

import md5 from "md5";

const config = proxy.ipConfig;

function rightClick() {
  uni.navigateBack();
}
const activeName = ref("");
onLoad((options) => {
  if (options.activeName) activeName.value = options.activeName;
});
// 密码是否明文
const showConfirmPassword = ref(true);

const name = ref("");
const password = ref("");

const submitStatus = computed(() => {
  return !!(name.value && password.value);
});

// 小程序版本号
const version = computed(() => {
  let v = "";

  // #ifdef MP-WEIXIN
  v = uni.getAccountInfoSync().miniProgram.version;
  // #endif
  return v;
});

function submitHandle() {
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/auth/bind/login_" + new Date().getTime(),
      {
        account: name.value,
        password: md5(md5(password.value)),
      },
      {
        method: "post",
      }
    )
    .then((res) => {
      if (res.data.code === 2000) {
        uni.showToast({
          title: "登录成功",
          icon: "none",
          duration: 1500,
        });

        uni.setStorageSync("user", res.data.data);
        // 清空购物车选中
        uni.setStorageSync("selectGoods", JSON.stringify([]));
        uni.setStorageSync("selectFromFoot", JSON.stringify([]));
        // 清空缓存地址
        uni.setStorageSync("addressData", JSON.stringify({}));
        uni.setStorageSync("activeName", JSON.stringify(""));
        setTimeout(() => {
          if (activeName.value === "trading") {
            // 因商户没有购物车，统一跳转到首页
            uni.redirectTo({
              url: "/pages/indexView/index",
            });
          } else if (activeName.value === "ai") {
            // // #ifdef H5
            uni.redirectTo({
              url: "/pagesPackages3/aiAssistantView/h5index",
            });
            // // #endif
            // // #ifdef MP-WEIXIN
            uni.redirectTo({
              url: "/pagesPackages3/aiAssistantView/index",
            });
            // // #endif
          } else {
            uni.redirectTo({
              url: "/pages/indexView/index",
            });
          }
        }, 1500);
      } else {
        uni.showToast({
          title: res.data.message,
          icon: "none",
          duration: 1500,
        });
      }
    })
    .catch((error) => {
      uni.showToast({
        title: error.message || "网络错误，请稍后重试",
        icon: "none",
        duration: 1500,
      });
    });
}
</script>
<style scoped lang="scss">
::v-deep(.uni-input-input) {
  letter-spacing: 0px;
}

.wrap {
  height: 100vh;
  background: linear-gradient(
    211deg,
    #ffffff -35%,
    rgba(255, 255, 255, 0) 148%
  );
  background-size: cover;
  background-repeat: no-repeat;

  .logo-box {
    padding-top: 180rpx;
    font-size: 36rpx;
    font-weight: 500;
    color: #333333;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .logo {
      width: 240rpx;
      height: 240rpx;
    }
  }

  .form-box {
    padding: 0 30rpx;
    box-sizing: border-box;
    margin-top: 80rpx;

    .input-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      column-gap: 10rpx;
      width: 100%;
      height: 100rpx;
      border-radius: 80rpx;
      background: #fff;
      padding: 0 32rpx;
      box-sizing: border-box;
      margin-bottom: 60rpx;
    }

    .inner {
      display: block;
      width: calc(100% - 50rpx);
      height: 100%;
    }

    .icon {
      width: 48rpx;
      height: 48rpx;
    }

    .submit-box {
      button {
        display: block;
        background: linear-gradient(90deg, #068324 0%, #20ba45 100%);
        height: 88rpx;
        border-radius: 80rpx;
        color: #ffffff;
        font-size: 36rpx;
        border: none;
        outline: none;
      }

      button[disabled] {
        background: #e5e5e5 !important;
      }
    }
  }

  .tip-box {
    padding: 0 30rpx;
    box-sizing: border-box;
    margin-top: 20rpx;
    display: flex;
    gap: 10rpx;
    align-items: flex-start;
    color: #333333;
    font-size: 24rpx;

    .tip-icon {
      width: 36rpx;
      height: 36rpx;
    }
  }
}

.eye {
  width: 48rpx;
  height: 48rpx;
}

// 消除微软浏览器自带的密码小眼睛
    .uni-input-input {   
        & ::-ms-reveal{  
    display: none;    

        }  
        & ::-ms-clear{  
    display: none;    

        }  
  } 
</style>
