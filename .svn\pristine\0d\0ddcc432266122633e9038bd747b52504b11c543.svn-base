<template>
    <view class="main-box">
        <iframe :src="charts.src" class="iframe-box" />
    </view>
</template>
<script setup>
import { reactive } from "vue";
import { onLoad,} from '@dcloudio/uni-app';
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
const config = proxy.ipConfig;
// 导入ipConfig
const charts = reactive({
    src: '',
})
onLoad((option) => {
    let token=uni.getStorageSync("user")?.token
    charts.src=config.syCharts+`?productId=${option.productId}&productName=${option.productName}&token=${token}`
})
</script>
<style scoped lang="scss">
.main-box {
    width: 100%;
    height: 100vh;
    .iframe-box{
        width: 100%;
        height: 100%;
    }
}
</style>
