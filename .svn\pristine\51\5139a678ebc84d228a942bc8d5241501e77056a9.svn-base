<template>
  <view
    class="content"
    :style="
      'background-image:url(' +
      config.imageUrl +
      '/myPage/bgc2.png);padding-top:' +
      safeAreaTop +
      'px;background-position: center ' +
      (-191.86 + safeAreaTop) +
      'rpx'
    "
  >
    <!-- 顶部 -->
    <view class="navigationBar">
      <view class="navigationBarback"> </view>
      <view class="navigationBarTitleText"> </view>
      <view class="navigationBarTitleall"> </view>
    </view>
    <!-- 用户信息 -->
    <view class="userinfo" @click="gotoPersonalInfo">
      <view class="avatarImage">
        <!-- <button
          class="avatarImage_btn"
          open-type="chooseAvatar"
          @chooseavatar="onChooseAvatar"
        > -->
        <image
          class="up-avatarImage"
          width="100%"
          height="100%"
          style="border-radius: 50%"
          :src="avatarUrl"
          shape="circle"
        ></image>
        <!-- </button> -->
      </view>
      <view class="userinfo-right">
        <!-- <button open-type="getUserProfile" data-eventsync="true" @getuserprofile="onhandleUserProfile" class="user-btn">
          点击授权获取昵称
        </button> -->
        <view class="name">
          {{ userinfoData.name }}
        </view>
        <view class="phoneNumber">
          {{ userinfoData.phoneNumber }}
        </view>
      </view>
    </view>
    <!-- 订单 -->
    <view class="order">
      <!-- 结算数据商户 -->
      <view class="order-card" v-if="isMerchant == 2">
        <view class="card-top">
          <view class="card-top-title">结算数据</view>
          <view class="card-top-more" @click="gotoshsettlementCenter()"
            >查看详情
            <image
              style="width: 10rpx; height: 20rpx; margin-left: 10rpx"
              :src="config.imageUrl + '/myPage/arrowRight.svg'"
            >
            </image>
          </view>
        </view>
        <view class="orderList">
          <view
            @click="gotoOtherPage(item)"
            class="orderItem orderItem-new orderItem-new1"
          >
            <view class="orderItem-number"
              >{{ merchantSettlementData?.dds }}
            </view>
            <view class="orderItem-text orderItem-text1"> 完成订单数 </view>
          </view>
          <view
            @click="gotoOtherPage(item)"
            class="orderItem orderItem-new orderItem-new2"
          >
            <view class="orderItem-number">
              {{ merchantSettlementData?.djsJe }}
            </view>
            <view class="orderItem-text orderItem-text1">
              待结算金额(万元)
            </view>
          </view>
          <view
            @click="gotoOtherPage(item)"
            class="orderItem orderItem-new orderItem-new3"
          >
            <view class="orderItem-number">
              {{ merchantSettlementData?.yjsJe }}
            </view>
            <view class="orderItem-text orderItem-text1">
              已结算金额(万元)
            </view>
          </view>
        </view>
      </view>
      <!-- 结算数据养殖户 -->
      <view class="order-card" v-if="isMerchant == 1">
        <view class="card-top">
          <view class="card-top-title">卖鱼结算数据</view>
          <view class="card-top-more" @click="gotosettlementCenter()"
            >查看详情
            <image
              style="width: 10rpx; height: 20rpx; margin-left: 10rpx"
              :src="config.imageUrl + '/myPage/arrowRight.svg'"
            >
            </image>
          </view>
        </view>
        <view class="orderList">
          <view
            @click="gotoOtherPage(item)"
            class="orderItem orderItem-new orderItem-new1"
          >
            <view class="orderItem-number"
              >{{ merchantSettlementData2?.orderNumber }}
            </view>
            <view class="orderItem-text orderItem-text1"> 完成订单数 </view>
          </view>
          <view
            @click="gotoOtherPage(item)"
            class="orderItem orderItem-new orderItem-new2"
          >
            <view class="orderItem-number">
              {{ merchantSettlementData2?.settlementNo }}
            </view>
            <view class="orderItem-text orderItem-text1">
              待结算金额(万元)
            </view>
          </view>
          <view
            @click="gotoOtherPage(item)"
            class="orderItem orderItem-new orderItem-new3"
          >
            <view class="orderItem-number">
              {{ merchantSettlementData2?.settlementYes }}
            </view>
            <view class="orderItem-text orderItem-text1">
              已结算金额(万元)
            </view>
          </view>
        </view>
      </view>
      <!-- 商城订单 -->
      <view class="order-card order-card1" v-if="isMerchant != 2">
        <view class="card-top">
          <view class="card-top-title">商城订单</view>
          <view class="card-top-more" @click="gotoAllorder()"
            >全部订单
            <image
              style="width: 10rpx; height: 20rpx; margin-left: 10rpx"
              :src="config.imageUrl + '/myPage/arrowRight.svg'"
            >
            </image>
          </view>
        </view>
        <view class="orderList">
          <view
            @click="gotoOtherPage(item)"
            v-for="(item, index) in orderListData.mallData"
            class="orderItem"
            :key="index"
          >
            <view class="orderItem-icon">
              <image
                class="up-avatarImage"
                width="100%"
                height="100%"
                :src="config.imageUrl + item.src"
                shape="circle"
              >
              </image>
            </view>
            <view class="orderItem-text">
              {{ item.name }}
            </view>
          </view>
        </view>
      </view>
      <!-- 我的订单 -->
      <view class="order-card order-card2" v-if="isMerchant == 2">
        <view class="card-top">
          <view class="card-top-title">我的订单</view>
          <view class="card-top-more" @click="gotoshFishorder()"
            >全部订单
            <image
              style="width: 10rpx; height: 20rpx; margin-left: 10rpx"
              :src="config.imageUrl + '/myPage/arrowRight.svg'"
            >
            </image>
          </view>
        </view>
        <view class="orderList">
          <view
            @click="gotomyOrderView(item)"
            v-for="(item, index) in orderListData.shorderData"
            :key="index"
            class="orderItem"
          >
            <view class="orderItem-icon">
              <image
                class="up-avatarImage"
                width="100%"
                height="100%"
                :src="config.imageUrl + item.src"
              >
              </image>
            </view>
            <view class="orderItem-text">
              {{ item.name }}
            </view>
          </view>
        </view>
      </view>
      <!-- 卖鱼订单 -->
      <view class="order-card order-card2" v-if="isMerchant == 1">
        <view class="card-top">
          <view class="card-top-title">卖鱼订单</view>
          <view class="card-top-more" @click="gotoFishorder()"
            >全部订单
            <image
              style="width: 10rpx; height: 20rpx; margin-left: 10rpx"
              :src="config.imageUrl + '/myPage/arrowRight.svg'"
            >
            </image>
          </view>
        </view>
        <view class="orderList">
          <view
            @click="gotomyddPage(item)"
            v-for="(item, index) in orderListData.salesData"
            :key="index"
            class="orderItem"
          >
            <view class="orderItem-icon">
              <image
                class="up-avatarImage"
                width="100%"
                height="100%"
                :src="config.imageUrl + item.src"
                shape="circle"
              >
              </image>
            </view>
            <view class="orderItem-text">
              {{ item.name }}
            </view>
          </view>
        </view>
      </view>
      <!-- 我的需求 -->
      <view class="order-card order-card2" v-if="isMerchant == 2">
        <view class="card-top">
          <view class="card-top-title">我的需求</view>
          <view class="card-top-more" @click="gotomyNeedsView()"
            >全部需求
            <image
              style="width: 10rpx; height: 20rpx; margin-left: 10rpx"
              :src="config.imageUrl + '/myPage/arrowRight.svg'"
            >
            </image>
          </view>
        </view>
        <view class="orderList">
          <view
            @click="gotomyNeedsDetail(item)"
            v-for="(item, index) in orderListData.needsData"
            :key="index"
            class="orderItem"
          >
            <view class="orderItem-icon">
              <image
                class="up-avatarImage"
                width="100%"
                height="100%"
                :src="config.imageUrl + item.src"
                shape="circle"
              >
              </image>
            </view>
            <view class="orderItem-text">
              {{ item.name }}
            </view>
          </view>
        </view>
      </view>

      <!-- 其他服务 -->
      <view class="order-card order-card3" v-if="isMerchant != 999">
        <view class="card-top">
          <view class="card-top-title">其他服务</view>
        </view>
        <!-- 商户 -->
        <view class="orderList orderListsh" v-if="isMerchant == 2">
          <view
            @click="gotoOtherPage(item)"
            v-for="(item, index) in orderListData.shotherData"
            class="orderItem"
            :key="index"
          >
            <view class="orderItem-icon">
              <image
                class="up-avatarImage"
                width="100%"
                height="100%"
                :src="config.imageUrl + item.src"
                shape="circle"
              >
              </image>
            </view>
            <view class="orderItem-text">
              {{ item.name }}
            </view>
          </view>
          <!-- <view v-for="(item, index) in 2" class="orderItem">

          </view> -->
        </view>
        <view class="orderList" v-if="isMerchant == 1">
          <view
            @click="gotoOtherPage(item)"
            v-for="(item, index) in orderListData.otherData"
            class="orderItem"
            :key="index"
          >
            <view class="orderItem-icon">
              <image
                class="up-avatarImage"
                width="100%"
                height="100%"
                :src="config.imageUrl + item.src"
                shape="circle"
              >
              </image>
            </view>
            <view class="orderItem-text">
              {{ item.name }}
            </view>
          </view>
          <!-- <view v-for="(item, index) in 2" class="orderItem">

          </view> -->
        </view>
      </view>
    </view>

    <!-- <view class="text-area">
      <up-button text="确定" @click="confirm"></up-button>
    </view> -->
  </view>

  <!-- 版本号显示 -->
  <view class="version-info" v-if="version">当前版本号：V{{ version }}</view>

  <ndb-foot activeName="my"></ndb-foot>
  <up-toast ref="uToastRef"></up-toast>
  <up-notify message="功能开发中..." ref="uNotifyRef"></up-notify>
  <up-modal
    title="确定退出登录?"
    :show="showModal"
    @cancel="cancelFn"
    @confirm="confirmFn"
    confirmColor="#068324"
    ref="uModal"
    :asyncClose="true"
    :showCancelButton="true"
  ></up-modal>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import {
  onLoad,
  onShow,
  // onReachBottom,
  // onPullDownRefresh,
} from "@dcloudio/uni-app";

// ------------退出登录
// 创建响应式数据
const showModal = ref(false);
const safeAreaTop = ref(0);
// 方法
const showModalFn = () => {
  showModal.value = true;
};
const confirmFn = () => {
  logout();
};
//判断商户养殖户
let isMerchant = ref("");
onLoad((options) => {
  // const systemInfo = uni.getSystemSetting();
  const systemInfo = uni.getSystemInfoSync();
  safeAreaTop.value = systemInfo.statusBarHeight || 0;
  let user = uni.getStorageSync("user");
  isMerchant.value = user.yhlx;
  if (isMerchant.value == 2) {
    // 商户
    getMerchantSettlementData();
  } else {
    // 养殖户
    getMerchantSettlementData2();
  }
});

// 退出登录
const logout = () => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/grzx/qtfw/logout",
      {},
      {
        method: "POST",
      }
    )
    .then((res) => {
      try {
        uni.removeStorageSync("user");
        setTimeout(() => {
          showModal.value = false;
          uni.redirectTo({
            url: "/pages/loginView/index",
          });
        }, 500);
      } catch (e) {
        console.error("删除失败", e);
      }
    });
};
// 取消
const cancelFn = () => {
  showModal.value = false;
};
// ------------退出登录
const uToastRef = ref(null);
const uNotifyRef = ref(null);
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";

var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl1;
}

// 确定
function confirm(params) {
  return;
  http
    .post(
      proxy.ipConfig.baseUrl1 + "/myPage/getUserData",
      {
        // areaId: "",
        // pageSize: "10",
        // pageIndex: 1,
      },
      {
        method: "GET",
      }
    )
    .then((res) => {
      // if (res.data.code == 200 && res.data.data != null) {
      //   if (res.data.data.isPopup == 1) {
      //     page.isShowTip = true;
      //     tipImg.value = res.data.data.picUrl;
      //   }
      // } else {
      //   page.isShowTip = false;
      // }
    });
}
//用户信息
let userinfoData = ref({
  name: "",
  phoneNumber: "",
  avatarImage: "",
});

let avatarUrl = ref(config.imageUrl + "/myPage/tx.svg");
//获取用户数据
const getUserinfo = () => {
  // let user = uni.getStorageSync("user");
  // userinfoData.value.name = user.userName;
  // return;
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/grzx/grxx/getMemberInfo",
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        userinfoData.value.name = data.nickname;
        userinfoData.value.account = data.account;
        if (data.icon) avatarUrl.value = data.icon;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
//订单信息
let orderListData = ref({
  mallData: [],
  shorderData: [
    {
      id: "",
      code: "013",
      name: "全部",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/needicon1.svg",
    },
    {
      id: "0",
      code: "013",
      name: "待出货",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/shicon2.svg",
    },
    {
      id: "1",
      code: "013",
      name: "待收货",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/shicon3.svg",
    },
    {
      id: "2",
      code: "013",
      name: "已完成",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/shicon4.svg",
    },
    {
      id: "3",
      code: "013",
      name: "已取消",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/shicon5.svg",
    },
  ],
  needsData: [
    {
      id: "",
      code: "013",
      name: "全部",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/needicon1.svg",
    },
    {
      id: "0",
      code: "013",
      name: "待分配",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/needicon2.svg",
    },
    {
      id: "1",
      code: "013",
      name: "待接单",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/needicon3.svg",
    },
    {
      id: "2",
      code: "013",
      name: "已接单",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/needicon4.svg",
    },
    {
      id: "3",
      code: "013",
      name: "已取消",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/shicon5.svg",
    },
  ],
  salesData: [
    {
      id: "99",
      code: "013",
      name: "未接需求",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/myicon1.svg",
    },
    {
      id: "0",
      code: "013",
      name: "待出货",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/myicon2.svg",
    },
    {
      id: "1",
      code: "013",
      name: "待收货",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/myicon3.svg",
    },
    {
      id: "2",
      code: "013",
      name: "已完成",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/myicon4.svg",
    },
    {
      id: "3",
      code: "013",
      name: "已取消",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/myicon5.svg",
    },
  ],
  otherData: [
    {
      id: "12011",
      code: "022111",
      name: "我的协议",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/iconxy.svg",
    },
    {
      id: "011",
      code: "011",
      name: "收货地址",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/shdz.svg",
    },
    {
      id: "012",
      code: "012",
      name: "帮助与反馈",
      mall: false,
      sales: false,
      other: true,
      isMerchant: 2,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon12.svg",
    },
    {
      id: "013",
      code: "013",
      name: "我的贷款",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/wddk.svg",
    },
    {
      id: "013",
      code: "013",
      name: "退出登录",
      mall: false,
      sales: false,
      other: true,
      isMerchant: 2,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon13.svg",
    },
  ],
  shotherData: [
    {
      id: "12011",
      code: "022111",
      name: "我的协议",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/iconxy.svg",
    },
    {
      id: "012",
      code: "012",
      name: "帮助与反馈",
      mall: false,
      sales: false,
      other: true,
      isMerchant: 2,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon12.svg",
    },

    {
      id: "013",
      code: "013",
      name: "退出登录",
      mall: false,
      sales: false,
      other: true,
      isMerchant: 2,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon13.svg",
    },
  ],
});
const getOrderList = () => {
  let data = [
    {
      id: "001",
      code: "001",
      name: "待付款",
      mall: true,
      sales: false,
      other: false,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon1.svg",
    },
    {
      id: "002",
      code: "002",
      name: "待发货",
      mall: true,
      sales: true,
      other: false,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon2.svg",
    },
    {
      id: "003",
      code: "003",
      name: "待收货",
      mall: true,
      sales: true,
      other: false,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon3.svg",
    },
    {
      id: "004",
      code: "004",
      name: "退款售后",
      mall: true,
      sales: false,
      other: false,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon4.svg",
    },
    {
      id: "005",
      code: "005",
      name: "待接单",
      mall: false,
      sales: true,
      other: false,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon5.svg",
    },
    {
      id: "006",
      code: "006",
      name: "待结算",
      mall: false,
      sales: true,
      other: false,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon6.svg",
    },
    {
      id: "007",
      code: "007",
      name: "待提现",
      mall: false,
      sales: true,
      other: false,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon7.svg",
    },
    // {
    //   id: "008",
    //   code: "008",
    //   name: "溯源登记",
    //   mall: false,
    //   sales: false,
    //   other: true,
    //   opertName: "丹阳市管理员",
    //   opertTime: "2020-03-03 16：18",
    //   src: "/myPage/icon8.svg",
    // },
    // {
    //   id: "009",
    //   code: "009",
    //   name: "信用额度",
    //   mall: false,
    //   sales: false,
    //   other: true,
    //   opertName: "丹阳市管理员",
    //   opertTime: "2020-03-03 16：18",
    //   src: "/myPage/icon9.svg",
    // },
    // {
    //   id: "010",
    //   code: "010",
    //   name: "养殖户认证",
    //   mall: false,
    //   sales: false,
    //   other: true,
    //   opertName: "丹阳市管理员",
    //   opertTime: "2020-03-03 16：18",
    //   src: "/myPage/icon10.svg",
    // },
    {
      id: "12011",
      code: "022111",
      name: "我的协议",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/iconxy.svg",
    },
    {
      id: "011",
      code: "011",
      name: "收货地址",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/shdz.svg",
    },
    {
      id: "012",
      code: "012",
      name: "帮助与反馈",
      mall: false,
      sales: false,
      other: true,
      isMerchant: 2,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon12.svg",
    },
    {
      id: "013",
      code: "013",
      name: "我的贷款",
      mall: false,
      sales: false,
      other: true,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/wddk.svg",
    },
    {
      id: "013",
      code: "013",
      name: "退出登录",
      mall: false,
      sales: false,
      other: true,
      isMerchant: 2,
      opertName: "丹阳市管理员",
      opertTime: "2020-03-03 16：18",
      src: "/myPage/icon13.svg",
    },
  ];
  orderListData.value.mallData = data.filter((el) => el.mall == true);
  // orderListData.value.otherData = data.filter((el) => el.other == true);
};
//全部订单
const gotoAllorder = () => {
  uni.navigateTo({
    url: "/pagesPackages3/mallOrderView/index?currentIndex=0",
  });
};
// 我的订单商户
const gotoshFishorder = () => {
  uni.navigateTo({
    url: "/pagesPackages3/shfishOrderView/index",
  });
};
// 卖鱼订单养殖户
const gotoFishorder = () => {
  uni.navigateTo({
    url: "/pagesPackages3/fishOrderView/index",
  });
};
//我的需求
const gotomyNeedsView = () => {
  uni.navigateTo({
    url: "/pagesPackages3/myNeedsView/shindex",
  });
};
//跳转指定功能
const gotoOtherPage = (item) => {
  switch (item.name) {
    case "待付款":
      uni.navigateTo({
        url: "/pagesPackages3/mallOrderView/index?currentIndex=1",
      });
      break;
    case "待发货":
      uni.navigateTo({
        url: "/pagesPackages3/mallOrderView/index?currentIndex=2",
      });
      break;
    case "待收货":
      uni.navigateTo({
        url: "/pagesPackages3/mallOrderView/index?currentIndex=3",
      });
      break;
    case "退款售后":
      uni.navigateTo({
        url: "/pagesPackages3/mallOrderView/index?currentIndex=4",
      });
      break;
    case "我的协议":
      uni.navigateTo({
        url: "/pagesPackages3/myAgreementView/index",
      });
      break;
    case "收货地址":
      uni.navigateTo({
        url: "/pagesPackages2/receiveAddressView/index",
      });
      break;
    case "帮助与反馈":
      uni.navigateTo({
        url: "/pagesPackages3/helpAndfeedbackView/index",
      });
      break;
    case "我的贷款":
      uni.navigateTo({
        url: "/pagesPackages3/myLoanView/index",
      });
      break;
    case "未接需求":
      uni.navigateTo({
        url: "/pagesPackages3/myNeedsView/index",
      });
      break;

    case "退出登录":
      showModalFn();
      break;
    default:
      uToastRef.value.show({
        type: "error",
        icon: false,
        title: "失败主题",
        message: "功能正在开发中",
        iconUrl: "https://uview-plus.jiangruyi.com/resources/toast/error.png",
      });
      break;
  }

  // uni.redirectTo({
  //   url: '/pagesPackages3/testView1/index'
  // });
};
// 跳转指定需求
const gotomyNeedsDetail = (item) => {
  uni.navigateTo({
    url: "/pagesPackages3/myNeedsView/shindex?status=" + item.id,
  });
};
//跳转我的订单
const gotomyOrderView = (item) => {
  uni.navigateTo({
    url: "/pagesPackages3/shfishOrderView/index?status=" + item.id,
  });
};
// 跳转卖鱼订单
const gotomyddPage = (item) => {
  if (item.id == 99) {
    uni.navigateTo({
      url: "/pagesPackages3/myNeedsView/index",
    });
  } else {
    uni.navigateTo({
      url: "/pagesPackages3/fishOrderView/index?status=" + item.id,
    });
  }
};

getConfig();
getUserinfo(); //用户
getOrderList(); //订单
// 头像上传
const onChooseAvatar = (e) => {
  return;
  avatarUrl.value = e.detail.avatarUrl;
  uploadAvatar(); // 上传头像到服务器获取真实地址
  updateUser();
};
const uploadAvatar = () => {};
// 跳转个人信息页面
const gotoPersonalInfo = () => {
  uni.navigateTo({
    url: "/pagesPackages3/personalInfoView/index",
  });
};
//// 跳转商户结算中心页面
const gotoshsettlementCenter = () => {
  uni.navigateTo({
    url: "/pagesPackages3/shsettlementCenterView/index",
  });
};
// 跳转养殖户结算中心页面
const gotosettlementCenter = () => {
  uni.navigateTo({
    url: "/pagesPackages3/settlementCenterView/index",
  });
};
//获取用户信息
// 用户昵称存储
const nickname = ref("");
// 用户信息回调
const onhandleUserProfile = (e) => {};
// 用户更新接口
const updateUser = () => {
  http
    .post(
      proxy.ipConfig.baseUrl1 + "/myPage/updateUserData",
      {
        name: userinfoData.value.name,
        phoneNumber: userinfoData.value.phoneNumber,
        avatarImage: avatarUrl.value,
      },
      {
        method: "POST",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};

// 小程序版本号
const version = computed(() => {
  let v = "";
  // #ifdef MP-WEIXIN
  v = uni.getAccountInfoSync().miniProgram.version;
  // #endif
  return v;
});

// 获取商户结算数据
let merchantSettlementData = ref({});
const getMerchantSettlementData = () => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/grzxSh/getShJsData",
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        merchantSettlementData.value = res.data.data;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
//获取养殖户数据
let merchantSettlementData2 = ref({});
const getMerchantSettlementData2 = () => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/farmersHome/settlementOverview",
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        merchantSettlementData2.value = res.data.data;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
</script>

<style scoped>
.content {
  display: flex;
  align-items: center;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: wrap;
  position: relative;
  /* background-image: url(/images/myPage/bgc2.png); */
  /* background-image: url(/images/myPage/bgc3.svg); */
  background-size: 838.95rpx 649.19rpx;
  /* background-size: contain; */
  /* 覆盖容器但保持宽高比‌:ml-citation{ref="5" data="citationList"} */
  background-position: center -191.86rpx;
  /* 垂直位置下移25%，显示下半部分‌:ml-citation{ref="4,6" data="citationList"} */
  background-repeat: no-repeat;
}

.navigationBar {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  justify-content: space-between;
  position: fixed;
  left: 0;
  top: 0;
}

.navigationBarTitleText {
  height: 100%;
  opacity: 1;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

/* 用户信息 */
.userinfo {
  width: 700rpx;
  height: 112rpx;

  /* background-color: #008443; */
  border-radius: 3.49rpx;
  /* box-shadow: 0px 1.74rpx 8.72rpx 0px rgba(202, 204, 204, 0.2); */
  font-size: 24.42rpx;
  padding: 0rpx 34.88rpx;
  display: flex;
  align-items: center;
  margin-top: 88rpx;
}

.avatarImage {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  /* border: 1.74rpx solid #fff; */
  margin-right: 20.93rpx;
}

:deep(.avatarImage .u-button),
.avatarImage_btn {
  width: 112rpx;
  height: 112rpx;
  background-color: transparent;
  padding: 0;
  border: none;
}

:deep(.avatarImage .u-button:after),
.avatarImage_btn:after {
  display: none;
}

:deep(.avatarImage .u-button:before),
.avatarImage_btn:before {
  display: none;
}

.up-avatarImage {
  width: 100%;
  height: 100%;
  /* border-radius: 50%; */
}

.userinfo-right {
  width: 401.16rpx;
  height: 112rpx;
  color: #fff;
}

.name {
  width: 226.74rpx;
  height: 48.84rpx;
  line-height: 48.84rpx;
  border-radius: 47.09rpx;
  /* background-color: rgba(119, 118, 118, 0.5); */
  /* color: #fff; */
  font-size: 32rpx;
  text-align: center;
  /* box-shadow: 0px 1.74rpx 8.72rpx 0px rgba(202, 204, 204, 0.2); */
  color: #333333;
  text-align: left;
}

.phoneNumber {
  width: 226.74rpx;
  height: 45.35rpx;
  line-height: 41.86rpx;
  font-size: 34.88rpx;
  margin-top: 10.47rpx;
  color: #000;
}

/* 订单部分 */
.order {
  width: 700rpx;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 17.44rpx;
  margin-bottom: 156.98rpx;
  margin-top: 32rpx;
}

.order-card {
  width: 700rpx;
  min-height: 230rpx;
  line-height: 34.88rpx;
  padding: 28rpx 20rpx 0px;
  border-radius: 17.44rpx;
  background-color: rgba(255, 255, 255, 1);
  color: rgba(16, 16, 16, 1);
  font-size: 24.42rpx;
  text-align: center;
  box-shadow: 0px 0rpx 8rpx 0px rgba(21, 43, 29, 0.1);
  /* border: 1.74rpx solid rgba(0, 0, 0, 0.07); */
  box-sizing: border-box;
}
.order-card1,
.order-card2,
.order-card3 {
  margin-top: 31.4rpx;
}

.card-top {
  width: 100%;
  height: 41.86rpx;
  display: flex;
  justify-content: space-between;
}

.card-top-title,
.card-top-more {
  height: 42rpx;
  line-height: 42rpx;
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.card-top-title {
  font-size: 32rpx;
  color: #333;
}

.orderList {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 28rpx;
}
.orderListsh {
  justify-content: flex-start;
}
.orderItem {
  width: 20%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  cursor: pointer;
  margin-bottom: 20rpx;
}
.orderItem-new {
  /* width: 33.33%; */
}
.orderItem-new1 {
  width: 130rpx;
}
.orderItem-new2 {
  width: 194rpx;
}
.orderItem-new3 {
  width: 194rpx;
}
.order-card1 .orderItem,
.order-card3 .orderItem {
  width: 25%;
  /* margin-bottom: 34.88rpx; */
}

.orderItem-icon {
  width: 60rpx;
  height: 60rpx;
}
.orderItem-number {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  z-index: 1;
}
.orderItem-text {
  width: 100%;
  height: 31.4rpx;
  margin-top: 17.44rpx;
  line-height: 31.4rpx;
  color: #333;
  font-size: 28rpx;
}
.orderItem-text1 {
  color: #666;
  font-size: 24rpx;
}
.version-info {
  color: #999;
  font-size: 24rpx;
  position: fixed;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}
</style>
