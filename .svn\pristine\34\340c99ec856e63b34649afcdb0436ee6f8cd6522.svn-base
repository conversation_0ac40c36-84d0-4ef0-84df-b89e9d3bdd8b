<template>
  <view class="content">
    <!-- <view class="background-box" :style="'background-image:url(' + config.imageUrl + '/index-nav-bg.png)'"></view> -->
    <view class="background-box" :style="'height:' + navHeight + 'px'"></view>
    <view class="main-custom-head-box" :style="'height:' + navHeight + 'px'">
      <view class="login-tip" @click="loginHandle" v-if="!token">
        <image class="user-icon" :src="config.imageUrl + '/login-user.png'" mode="aspectFit"></image>
        请登录
      </view>
      <view class="title">首页</view>
    </view>
    <scroll-view scroll-y="true" class="scroll" :style="'height: calc(100vh - 170rpx - ' + navHeight + 'px)'"
      @scrolltolower="myScrolltolower" refresher-enabled :refresher-triggered="triggerValue"
      @refresherrefresh="downPullRefresh">
      <view class="menu" v-if="yhlx == 2">
        <view class="item" style="height: 280rpx;"
          :style="'background-image: url(' + config.imageUrl + '/nav1_1Bg1.png)'" @click="gotoDemandReg">
          <view class="text" style="justify-content:flex-start;padding-top:34rpx">
            <text>我要买鱼</text>
            <view class="into">需求登记
              <image class="icon" :src="config.imageUrl + '/arrowrightWhite.png'" mode="aspectFit"> </image>
            </view>
          </view>
          <!-- <image :src="config.imageUrl + '/nav1_1.png'" mode="aspectFit" class="image"></image> -->
        </view>
        <view class="menuRol" style="width:340rpx;">
          <view class="item" @click="gotoNav1" :style="'background-image:url(' + config.imageUrl + '/nav1_1Bg2.png)'">
            <view class="text">
              <text>待我处理</text>
              <view class="into">去处理
                <image class="icon" :src="config.imageUrl + '/arrowrightWhite.png'" mode="aspectFit">
                </image>
              </view>
            </view>
            <view class="waitIsTrue" v-if="waitIsTrue">新</view>
            <!-- <image :src="config.imageUrl + '/nav2_2.png'" mode="aspectFit" class="image"></image> -->
          </view>
          <view class="item" @click="gotoAiPage()"
            :style="'background-image:url(' + config.imageUrl + '/nav1_1Bg3.png)'">
            <view class="text">
              <text>找专家</text>
              <view class="into">问题解答
                <image class="icon" :src="config.imageUrl + '/arrowrightWhite.png'" mode="aspectFit">
                </image>
              </view>
            </view>
            <!-- <image :src="config.imageUrl + '/nav2_2.png'" mode="aspectFit" class="image"></image> -->
          </view>
        </view>
      </view>
      <view class="menu" v-else>
        <view class="item" :style="'background-image:url(' + config.imageUrl + '/nav2_2Bg.png)'" @click="gotoNav1">
          <view class="text">
            <text>我要卖鱼</text>
            <view class="into">0抗好鱼
              <image class="icon" :src="config.imageUrl + '/arrowrightWhite.png'" mode="aspectFit">
              </image>
            </view>
          </view>
          <view class="image-placeholder"></view>
        </view>
        <view class="item" @click="gotoNav2" :style="'background-image:url(' + config.imageUrl + '/nav1Bg.png)'">
          <view class="text">
            <text>买饲料</text>
            <view class="into">优惠价格
              <image class="icon" :src="config.imageUrl + '/arrowrightWhite.png'" mode="aspectFit">
              </image>
            </view>
          </view>
          <view class="image-placeholder"></view>
        </view>
        <view style="margin-top: 20rpx" class="item" @click="gotoNav3"
          :style="'background-image:url(' + config.imageUrl + '/nav3_3Bg.png)'">
          <view class="text">
            <text>我要贷款</text>
            <view class="into">在线申请
              <image class="icon" :src="config.imageUrl + '/arrowrightWhite.png'" mode="aspectFit">
              </image>
            </view>
          </view>
          <!-- <image :src="config.imageUrl + '/nav2_2.png'" mode="aspectFit" class="image"></image> -->
        </view>
        <view style="margin-top: 20rpx" class="item" @click="gotoAiPage()"
          :style="'background-image:url(' + config.imageUrl + '/nav4Bg.png)'">
          <view class="text">
            <text>找专家</text>
            <view class="into">问题解答
              <image class="icon" :src="config.imageUrl + '/arrowrightWhite.png'" mode="aspectFit">
              </image>
            </view>
          </view>
          <!-- <image :src="config.imageUrl + '/nav2_2.png'" mode="aspectFit" class="image"></image> -->
        </view>
      </view>
      <view style="background: #f4f4f4">
        <view class="tabs-box" id="tabs-box">
          <view class="tabs-box-inner">
            <view class="item" v-for="(item, index) in tabsdata" :key="index" @click="handleTab(index)">
              <text :class="tabsCurrent == index ? 'act' : ''">{{
                item.title
              }}</text>
              <!-- <text :class="tabsCurrent == index ? 'act-bg' : ''" class="bot">{{ item.subTitle }}</text> -->
            </view>
          </view>
        </view>
        <view class="tab-content-wrap">
          <view v-if="noData" class="no-data-box">
            <!-- <image class="no-data-img" :src="config.imageUrl + '/no-data.png'" mode="aspectFit"></image> -->
            <view class="no-data-text">暂无数据</view>
          </view>
          <template v-else>
            <view class="tab-content" v-for="item in articleList" :key="item.name" @click="linkToDetail(item)">
              <image class="main-img" v-if="item.coverImage" :src="item.coverImage" mode="aspectFill"></image>
              <view class="main-text-box" :style="{ paddingTop: item.coverImage ? '0' : '20rpx' }">
                <view class="title">
                  {{ item.title }}
                </view>
                <view class="text">
                  {{ item.summary }}
                </view>
                <view class="bot-box">
                  <view class="icon-box" @click.stop="handleLike(item)">
                    <up-icon :name="item.liked ? 'thumb-up-fill' : 'thumb-up'"
                      :color="item.liked ? '#FF7B00' : ''"></up-icon>
                    点赞
                  </view>
                  <view class="icon-box" @click.stop="handleFavorite(item)">
                    <up-icon :name="item.favorited ? 'star-fill' : 'star'"
                      :color="item.favorited ? '#FF7B00' : ''"></up-icon>
                    收藏
                  </view>
                  <view class="icon-box">
                    <up-icon name="eye-fill"></up-icon>
                    {{ item.viewCount }}
                  </view>
                </view>
              </view>
            </view>
            <u-loadmore v-if="!noData" :status="loadStatus" />
          </template>
        </view>
      </view>
    </scroll-view>
  </view>
  <ndb-foot activeName="index"></ndb-foot>
</template>
<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";
import { getCurrentInstance } from "vue";
import { http, toast } from "uview-plus";
import ndbFoot from "@/components/ndb-foot/ndb-foot.vue";
import { useTokenPolling } from "./components/useTokenPolling.js";
import {
  onArticleStatusChanged,
  getArticleStatus,
  getArticleStatusSync,
} from "./components/useEventBus.js";
import eventBus from "@/http/eventBus.js";

// 获取全局配置和token
const { proxy } = getCurrentInstance();
const config = proxy.ipConfig;
const { token } = useTokenPolling();

// 页面状态
const navHeight = ref("44");
const Height = ref("");
const tabsCurrent = ref(0);
const initSearchVal = ref("");
const noData = ref(false);
const triggerValue = ref(false); // 添加triggerValue的定义

const pager = reactive({
  page: 1, // 当前页码
  size: 5, // 每页数量
  total: 0, // 总数据量
});
let loadStatus = ref("loading"); // 加载状态标识

let yhlx = ref(uni.getStorageSync('user')?.yhlx || 1); // 1养殖户 2商户
onMounted(() => {
  yhlx.value = uni.getStorageSync('user')?.yhlx || 1;
})
onShow(() => {
  if (yhlx.value == 2) getWaitMessNum()
  eventBus.$on("haveNewMsg", () => {
    if (yhlx.value == 2) getWaitMessNum()
  });
})

onHide(() => {
  eventBus.$off("haveNewMsg");
});

let waitIsTrue = ref(false); // 是否有新消息
function getWaitMessNum() {
  http.get(proxy.ipConfig.baseUrl2 + "/shsy/getShDclNewMsg", { method: "GET", }).then((res) => {
    if (res.data.code == 2000) {
      waitIsTrue.value = res.data.data
    }
  })
}

// 文章列表
const articleList = ref([]);

function linkToDetail(item) {
  // 记录当前点击的文章ID，用于后续状态更新
  // 使用条件编译处理不同平台
  // #ifdef H5
  if (typeof window !== "undefined") {
    window._currentArticleId = item.klgId;
  }
  // #endif

  // 使用uni-app的存储API，兼容所有平台
  uni.setStorageSync("current_article_id", item.klgId);

  uni.navigateTo({
    url: "/pages/articleDetailView/index?id=" + item.klgId,
  });
}

// 标签页数据
const tabsdata = ref([
  {
    title: "知识科普",
  },
]);

// 分类标签映射
const tagIdMap = reactive({
  yzym: "1912379242017047001", // 优质鱼苗
});

// 监听文章状态变更事件
const articleStatusListener = (data) => {
  try {
    // 查找并更新列表中对应的文章状态
    // 检查多种可能的ID匹配方式
    let article = null;

    // 先尝试通过事件中的ID查找
    if (data.klgId) {
      article = articleList.value.find((item) => item.klgId === data.klgId);
    }

    if (!article && data.id) {
      article = articleList.value.find((item) => item.klgId === data.id);
    }

    if (!article && data.knowledgeId) {
      article = articleList.value.find(
        (item) => item.klgId === data.knowledgeId
      );
    }

    // 如果还是没找到，尝试使用存储的当前文章ID
    if (!article) {
      let currentArticleId;

      // #ifdef H5
      if (typeof window !== "undefined" && window._currentArticleId) {
        currentArticleId = window._currentArticleId;
      }
      // #endif

      // 如果H5环境没有找到，或者是其他平台，尝试从存储获取
      if (!currentArticleId) {
        try {
          currentArticleId = uni.getStorageSync("current_article_id");
        } catch (err) {
          // 获取存储的文章ID失败
        }
      }

      if (currentArticleId) {
        article = articleList.value.find(
          (item) => item.klgId === currentArticleId
        );
      }
    }

    // 尝试其他ID字段匹配
    if (!article) {
      article = articleList.value.find(
        (item) =>
          (item.id &&
            (item.id === data.id ||
              item.id === data.klgId ||
              item.id === data.knowledgeId)) ||
          (item.knowledgeId &&
            (item.knowledgeId === data.id ||
              item.knowledgeId === data.klgId ||
              item.knowledgeId === data.knowledgeId))
      );
    }

    if (article) {
      if (data.type === "like") {
        article.liked = data.liked;
      } else if (data.type === "favorite") {
        article.favorited = data.favorited;
      }
    }
  } catch (error) {
    // 处理文章状态变更事件错误
  }
};

// 用于存储移除监听器的函数
let removeArticleStatusListener = null;

onLoad(() => {
  getDataList();
  // 对齐胶囊按钮
  // #ifdef MP-WEIXIN
  let menuButtonObject = uni.getMenuButtonBoundingClientRect();
  // 使用新的API代替被弃用的getSystemInfo
  if (uni.canIUse('getWindowInfo')) {
    const windowInfo = uni.getWindowInfo();
    let statusBarHeight = windowInfo.statusBarHeight;
    Height.value = menuButtonObject.height;
    navHeight.value =
      statusBarHeight +
      menuButtonObject.height +
      (menuButtonObject.top - statusBarHeight) * 2;
  } else {
    // 兼容低版本微信
    uni.getSystemInfo({
      success: (res) => {
        let statusBarHeight = res.statusBarHeight;
        Height.value = menuButtonObject.height;
        navHeight.value =
          statusBarHeight +
          menuButtonObject.height +
          (menuButtonObject.top - statusBarHeight) * 2;
      },
    });
  }
  // #endif

  // 使用新的事件监听方法
  try {
    removeArticleStatusListener = onArticleStatusChanged(articleStatusListener);
  } catch (error) {
    // 添加监听器失败
  }
});

onShow(async () => {

  // 检查是否有当前文章ID，使用uni-app存储API获取
  let currentId;

  try {
    // 优先从存储中获取
    currentId = uni.getStorageSync("current_article_id");

    // H5环境下也可以从window对象获取
    // #ifdef H5
    if (
      !currentId &&
      typeof window !== "undefined" &&
      window._currentArticleId
    ) {
      currentId = window._currentArticleId;
    }
    // #endif

    if (currentId) {
      // 尝试获取状态 - 处理异步/同步两种情况
      let status;
      try {
        // 先尝试同步获取
        status = getArticleStatusSync(currentId);

        // 如果同步获取失败，尝试异步获取
        if (!status) {
          status = await getArticleStatus(currentId);
        }

        // 更新文章状态
        if (status) {
          updateArticleStatus(currentId, status);
        }
      } catch (err) {
        // 获取文章状态失败
      }
    }
  } catch (err) {
    // 获取当前文章ID失败
  }

  if (initSearchVal.value === "") {
    initSearchVal.value = "请输入";
    setTimeout(() => (initSearchVal.value = ""), 50);
  }
});

// 更新文章状态的辅助函数
function updateArticleStatus(articleId, status) {
  const article = articleList.value.find(
    (item) =>
      item.klgId === articleId ||
      item.id === articleId ||
      item.knowledgeId === articleId
  );

  if (article) {
    if (status.type === "like" && status.liked !== undefined) {
      article.liked = status.liked;
    } else if (status.type === "favorite" && status.favorited !== undefined) {
      article.favorited = status.favorited;
    }
  }
}

// 页面卸载时移除事件监听
onUnload(() => {
  eventBus.$off("haveNewMsg");
  // 使用返回的移除函数清理监听器
  if (removeArticleStatusListener) {
    removeArticleStatusListener();
  }
});

// 导航方法
const gotoNav1 = () => {
  // uni.showToast({ title: "正在开发中~~", icon: "none" });
  if (!token.value) return uni.showToast({
    title: "请登录后操作",
    icon: 'none',
    mask: true,
    duration: 2000,
    success: () => {
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/loginView/index'
        })
      }, 2000)
    }
  })
  if (yhlx.value == 2) {
    uni.navigateTo({
      url: "/pagesPackages6/demandShView/index"
    });
  } else {
    uni.navigateTo({
      url: "/pagesPackages6/demandYzhView/index"
    });
  }
};

const gotoNav2 = () => {
  uni.navigateTo({
    url: "/pagesPackages4/shopView/index?tagId=" + tagIdMap.yzym,
  });
};

const gotoNav3 = () => {
  uni.navigateTo({
    url: "/pagesPackages5/financialSupermarketView/index",
  });
};

function gotoDemandReg() {
  if (!token.value) return uni.showToast({
    title: "请登录后操作",
    icon: 'none',
    mask: true,
    duration: 2000,
    success: () => {
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/loginView/index'
        })
      }, 2000)
    }
  })
  uni.reLaunch({
    url: "/pagesPackages6/demandRegisterView/index",
  });
}

const loginHandle = () => {
  uni.navigateTo({
    url: "/pages/loginView/index",
  });
};
const gotoAiPage = () => {
  // 使用条件编译处理不同平台的滚动
  // #ifdef H5
  uni.navigateTo({
    url: "/pagesPackages3/aiAssistantView/h5index",
  });
  // #endif
  // #ifdef MP-WEIXIN
  uni.navigateTo({
    url: "/pagesPackages3/aiAssistantView/index",
  });
  // #endif
};

// 标签页切换
const handleTab = (index) => {
  tabsCurrent.value = index;
};

function myScrolltolower(e) {
  let loadTotal = pager.page * pager.size;
  if (loadTotal < pager.total) {
    // 判断是否还有更多数据
    loadStatus.value = "loading"; // 设置加载状态
    pager.page++; // 页码加1
    setTimeout(() => {
      getShopList(); // 调用获取数据的方法
    }, 300);
  }
}

function getShopList() {
  let postObj = {
    page: pager.page,
    size: pager.size,
    // 其他查询参数...
  };
  http
    .post(proxy.ipConfig.baseUrl2 + "/knowledge/page", postObj, {
      method: "GET",
    })
    .then((res) => {
      articleList.value.push(...res.data.data.records); // 追加新数据到列表
      pager.total = res.data.data.total; // 更新总数据量

      // 判断是否还有更多数据
      if (pager.page * pager.size >= pager.total && pager.total > 0) {
        loadStatus.value = "nomore"; // 没有更多数据了
      } else if (pager.total > 0) {
        loadStatus.value = "loadmore"; // 还有更多数据
      }

      // 判断是否有数据
      if (articleList.value.length < 1) {
        noData.value = true;
        loadStatus.value = "nomore"; // 如果没有数据，也显示没有更多
      } else {
        noData.value = false;
      }
    })
    .catch(() => {
      loadStatus.value = "loadmore"; // 请求失败时，允许重试
    });
}

function getDataList() {
  pager.page = 1; // 重置页码
  articleList.value = []; // 清空列表
  getShopList(); // 重新获取数据
}

function downPullRefresh() {
  triggerValue.value = true;
  getDataList(); // 重新加载第一页数据
  setTimeout(() => {
    triggerValue.value = false;
    uni.showToast({
      title: "刷新成功",
      icon: "success",
      duration: 1000,
    });
  }, 1000);
}

// 添加mock数据方法
function getMockData(page, size) {
  const mockList = [];
  const startIndex = (page - 1) * size;

  for (let i = 0; i < size; i++) {
    const index = startIndex + i;
    if (index < 35) {
      // 总共模拟35条数据
      mockList.push({
        id: `${index + 1}`,
        title: `测试文章标题 ${index + 1}`,
        summary: `这是第${index + 1
          }篇测试文章的摘要内容，用于测试分页加载功能。这是一段较长的文字，用来测试显示效果。`,
        coverImage: config.imageUrl + "/indexNewsImag.png",
        liked: false,
        favorited: false,
        viewCount: Math.floor(Math.random() * 10000),
        klgId: `${index + 1}`,
      });
    }
  }

  return {
    records: mockList,
    total: 35, // 总数据量
    size: size,
    current: page,
  };
}

// 添加点赞和收藏方法
async function handleLike(item) {
  if (!token.value) {
    uni.showToast({
      title: "请登录后操作",
      icon: "none",
    });
    return;
  }

  try {
    const url = item.liked ? "/knowledge/cancelLike" : "/knowledge/like";
    await http.post(
      proxy.ipConfig.baseUrl2 + url + "?knowledgeId=" + item.klgId,
      {
        // knowledgeId: item.klgId
      }
    );

    item.liked = !item.liked;
    uni.showToast({
      title: item.liked ? "点赞成功" : "已取消点赞",
      icon: "none",
    });
  } catch (error) {
    uni.showToast({
      title: error,
      icon: "none",
    });
  }
}

async function handleFavorite(item) {
  if (!token.value) {
    uni.showToast({
      title: "请登录后操作",
      icon: "none",
    });
    return;
  }

  try {
    const url = item.favorited
      ? "/knowledge/cancelCollect"
      : "/knowledge/collect";
    await http.post(
      proxy.ipConfig.baseUrl2 + url + "?knowledgeId=" + item.klgId,
      {
        // knowledgeId: item.klgId
      }
    );

    item.favorited = !item.favorited;
    uni.showToast({
      title: item.favorited ? "收藏成功" : "已取消收藏",
      icon: "none",
    });
  } catch (error) {
    uni.showToast({
      title: error,
      icon: "none",
    });
  }
}
</script>
<style scoped lang="scss">
.content {
  padding-bottom: 170rpx;
  height: 100vh;
  // overflow-y: auto;
  overflow: hidden;
  box-sizing: border-box;
  // background: rgb(247, 247, 247);
}

.login-tip {
  position: absolute;
  left: 110rpx;
  bottom: 20rpx;
  transform: translateX(-50%);
  // font-size: 32rpx;
  // color: #333333;
  // font-weight: 500;
  display: flex;
  align-items: center;

  .user-icon {
    width: 44rpx;
    height: 44rpx;
    margin-right: 10rpx;
  }
}

.background-box {
  width: 100%;
  position: absolute;
  left: 0rpx;
  top: 0rpx;
  background-size: cover;
  background: linear-gradient(0deg, #7ddb9f -69%, #f5fff9 185%), #ffffff;
  // z-index: -1;
  // background: #32acff;
}

.main-custom-head-box {
  position: relative;

  .title {
    position: absolute;
    left: 50%;
    bottom: 20rpx;
    transform: translateX(-50%);
    font-size: 36rpx;
    color: #333333;
    font-weight: 550;
  }
}

.swiper-box {
  padding: 24rpx;
}

.menuRol {
  display: flex;
  flex-direction: column;
  gap: 20rpx 0;
}

.menu {
  width: 100%;
  flex-wrap: wrap;
  margin: auto;
  // min-height: 120rpx;
  // margin-top: 22rpx;
  display: flex;
  background: #fff;
  // background: linear-gradient(#FFF, #F7F8FB);
  padding: 24rpx 22rpx;
  box-sizing: border-box;
  // border-radius: 30rpx 30rpx 0rpx 0rpx;
  // gap: 20px 0px;
  column-gap: 24rpx;

  .item {
    position: relative;
    // width: 49%;
    flex: 1;
    gap: 0 20rpx;
    height: 140rpx;
    display: flex;
    // flex-direction: column;
    justify-content: space-between;
    align-items: center;
    position: relative;
    background-size: contain;
    background-repeat: no-repeat;
    padding-left: 24rpx;
    padding-right: 32rpx;
    box-sizing: border-box;

    .waitIsTrue {
      position: absolute;
      right: 0;
      top: 0;
      width: 32rpx;
      height: 32rpx;
      line-height: 32rpx;
      text-align: center;
      border-radius: 32rpx;
      background: #FF0000;
      box-shadow: 0px 0px 2px 2px rgba(237, 237, 237, 0.3);
      font-family: PingFang SC;
      font-size: 20rpx;
      color: #FFFFFF;
    }

    .image-placeholder {
      width: 96rpx;
      height: 96rpx;
    }

    .image {
      width: 96rpx;
      height: 96rpx;
    }

    .into {
      // position: absolute;
      // bottom: 15rpx;
      font-size: 24rpx;
      font-weight: 400;
      display: flex;
      align-items: center;
      gap: 12rpx;

      .icon {
        width: 20rpx;
        height: 20rpx;
      }
    }

    .text {
      width: 5em;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      // text-align: center;
      font-size: 28rpx;
      font-weight: 500;
      color: #ffffff;
    }
  }
}

.tabs-box {
  // background: rgb(247, 247, 247);
  font-size: 32rpx;
  color: #333;
  margin-top: 8rpx;

  .tabs-box-inner {
    position: relative;
    /* 关键定位 */
    display: flex;
    justify-content: center;
    align-items: center;
    // background: #FFF;
    height: 68rpx;
    z-index: 1;
    gap: 20rpx;
  }

  /* 使用伪元素创建渐变边框 */
  // .tabs-box-inner::before {
  // 	content: '';
  // 	position: absolute;
  // 	top: 0;
  // 	left: 0;
  // 	right: 0;
  // 	bottom: 0;
  // 	border-radius: 18px 18px 0 0;
  // 	border: 1px solid transparent;
  // 	border-bottom: none;
  // 	background: linear-gradient(180deg, #7DDB9F -11%, #F5FFF9 130%) border-box;
  // 	-webkit-mask:
  // 		linear-gradient(#fff, #fff) padding-box,
  // 		linear-gradient(#fff, #fff);
  // 	-webkit-mask-composite: xor;
  // 	mask-composite: exclude;
  // 	z-index: -1;
  // 	border-image: linear-gradient(180deg, #7DDB9F -11%, #F5FFF9 130%) 1 1 0 1;

  // }

  .act {
    // color: #068324;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    height: 100%;

    &::before {
      position: absolute;
      bottom: 0;
      content: "";
      left: 0;
      display: block;
      width: 50%;
      transform: translateX(50%);
      height: 8rpx;
      border-radius: 5rpx;
      background-color: #068324;
    }
  }

  .act-bg {
    background-color: #068324;
    color: #fff;
    border-radius: 20rpx;
    padding: 5rpx 15rpx;
  }

  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 130rpx;
    overflow: hidden;
    height: 100%;

    .bot {
      color: "#666666";
      font-size: 20rpx;
    }
  }
}

.tab-content-wrap {
  padding-bottom: 30rpx;
  background: #f4f4f4;
  margin-top: 30rpx;
}

.main-text-box {
  padding: 0 24rpx;
}

.tab-content {
  margin: 0 24rpx;
  padding-bottom: 24rpx;
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;

  .main-img {
    width: 100%;
    border-radius: 20rpx;
    height: 280rpx;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }

  .title {
    font-size: 32rpx;
    color: #333333;
    line-height: 1.5;
  }

  .text {
    color: #666666;
    font-size: 24rpx;
    line-height: 1.5;
  }

  .bot-box {
    display: flex;
    justify-content: flex-end;
    color: #666666;
    font-size: 24rpx;
    align-items: center;
    gap: 20rpx;
    margin-top: 30rpx;
  }

  .icon-box {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5rpx;
  }
}

.no-data-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  .no-data-img {
    width: 200rpx;
    height: 200rpx;
  }

  .no-data-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}
</style>
