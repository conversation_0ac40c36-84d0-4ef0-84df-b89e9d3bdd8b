import { createSSRApp } from "vue";
import App from "./App.vue";

import uviewPlus from "uview-plus";

import { initRequest } from "./http/index.js";
import { initWebSocket } from '@/http/webSocket.js'

// import socketDialog from "@/components/ndb-socket-message/index.vue"; // socket消息弹窗
import socketDialog from "@/components/ndb-polling-message/index.vue"; // 轮询消息弹窗

// #ifdef MP-WEIXIN
var config = require("./static/config.js"); // import { ipConfig } from '@/static/config';
// #endif

// #ifdef H5
// import config from 'http://*************:9754/static/config.js';
// #endif

export function createApp() {
  const app = createSSRApp(App);

  // #ifdef MP-WEIXIN
  app.config.globalProperties.ipConfig = config.ipConfig;
  // #endif

  // #ifdef H5
  app.config.globalProperties.ipConfig = window.ipConfig;
  // #endif

  // 初始化http
  initRequest(app);

  // 初始化webSocket
  // app.config.globalProperties.socket = initWebSocket(app.config.globalProperties.ipConfig);

  app.component("socketDialog", socketDialog);

  app.use(uviewPlus);

  // if(!app.config.globalProperties.ipConfig.syCharts){
  //   uni.showToast({
  //     title: '请在config配置syCharts的值！',
  //     icon: 'none'
  //   })
  // }

  return {
    app,
  };
}
