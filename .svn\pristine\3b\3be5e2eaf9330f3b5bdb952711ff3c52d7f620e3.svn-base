<template>
  <view class="demandView">
    <view class="audioBtn" @click="audioStart">
      <image class="audioIcon" :src="config.imageUrl + '/shop/audioIcon.png'"></image>
      录音
    </view>
  </view>
  <AudioLoading ref="audioLoadingRef" :audioLoading="audioLoading" :audioTime="audioTime"
    :audioTimeLimit="audioTimeLimit" @audioEnd="audioEnd" />
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
// import Recorder from 'recorder-core';
// import 'recorder-core/src/engine/mp3'; // mp3 封装
// import 'recorder-core/src/engine/mp3-engine'; // mp3 编码核心模块
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
const config = proxy.ipConfig
const emit = defineEmits(['hideTabbar', "postAudio"])
import AudioLoading from './audioLoading.vue';
import eventBus from "@/http/eventBus.js";

const audioLoading = ref(false); //语音弹框
let audioTime = ref(0); //语音时间

let rec = null;
let audioTimer = ref(null); // 定时器(限制录音秒数)
let audioTimeLimit = ref(60); // 录音时间限制(秒)

let recorderManager = null;
let audioInterval = ref(null)

var page = reactive({
  mediaRecorder: null,
  audioChunks: [],
  audioBlob: null,
  duration: 0, // 录音时长
});

onMounted(() => {
  // #ifdef MP-WEIXIN
  audioInit__WX()
  // #endif
  // #ifndef MP-WEIXIN
  audioInit__H5New()
  // #endif
});

/*开始录制语音*/
const audioInit__WX = () => {
  audioTime.value = 0;
  recorderManager = uni.getRecorderManager();
}

const audioInit__H5New = () => {
}

const audioInit__H5 = e => {
  audioTime.value = 0;
  rec = Recorder({
    type: 'mp3',
    sampleRate: 16000, // 采样率hz，每秒音波震动次数，越大细节越丰富
    bitRate: 16, // 比特率kbps，越大音质越好
    onProcess(buffers, powerLevel, duration, sampleRate) {
      audioLoading.value = true;
      audioLoadingRef.value.show()
      emit("hideTabbar", true)
      audioTime.value = duration;
    },
  });
  rec.open(() => {
    console.log("初始化成功");
  }, (msg, isUserNotAllow) => {
    audioLoading.value = false;
    emit("hideTabbar", false)
    console.log('初始化失败: ' + msg);
    // alert('初始化失败:' + msg)
    // uni.showToast({
    //   title: "初始化失败" + msg,
    //   icon: 'none',
    //   duration: 2000,
    // })
  });
};

let audioLoadingRef = ref(null)
function audioStart() {
  eventBus.$emit('stopAudio')
  // #ifdef MP-WEIXIN
  audioStart__WX()
  // #endif
  // #ifndef MP-WEIXIN
  audioStart__H5New()
  // #endif
}

const audioStart__WX = () => {
  recorderManager.start({
    format: 'mp3'
  });
  recorderManager.onStart(() => {
    uni.setKeepScreenOn({
      keepScreenOn: true
    })
    console.log('录音开始');
    audioTime.value = 0
    clearInterval(audioInterval.value)
    audioInterval.value = setInterval(() => {
      audioTime.value = audioTime.value + 180
    }, 180);
    audioLoading.value = true;
    audioLoadingRef.value.show()
    emit("hideTabbar", true)
    clearTimeout(audioTimer.value);
    audioTimer.value = setTimeout(() => {
      audioEnd__WX()
    }, audioTimeLimit.value * 1000);
  });
  recorderManager.onResume((res) => {
    console.log("onResume", res);
  })
  recorderManager.onStop((res) => {
    uni.setKeepScreenOn({
      keepScreenOn: false
    })
    audioTime.value = 0
    clearInterval(audioInterval.value)
    const {
      tempFilePath, duration
    } = res; // 获取录音文件的临时路径
    console.log('录音停止，文件路径：', res);
    uploadAudio__WX(tempFilePath, Math.round(duration / 1000)) // 暂时注释了
  });
}

let wakeLock;
const audioStart__H5New = async () => {
  try {
    wakeLock = await navigator.wakeLock.request('screen')
    console.log('屏幕常亮已开启');
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    // 录音
    page.mediaRecorder = new MediaRecorder(stream);
    page.mediaRecorder.ondataavailable = (event) => {
      page.audioChunks = [event.data];
    };
    page.mediaRecorder.start();
    audioTime.value = 0
    // 计时器
    clearInterval(audioInterval.value)
    audioInterval.value = setInterval(() => {
      audioTime.value = audioTime.value + 180
    }, 180);
    audioLoading.value = true;
    emit("hideTabbar", true)
    audioLoadingRef.value.show()
    // 限制录音时间
    clearTimeout(audioTimer.value);
    audioTimer.value = setTimeout(() => {
      audioEnd__H5New()
    }, audioTimeLimit.value * 1000);
    page.mediaRecorder.onstop = (res) => {
      console.log("wakeLock", wakeLock);

      wakeLock.release().then(function () {
        console.log('屏幕常亮已关闭');
      }).catch(function (error) {
        console.log('关闭屏幕常亮错误：', error);
      });
      page.audioBlob = new Blob(page.audioChunks, { type: "audio/mpeg" });
      uploadAudio__H5(page.audioBlob, Math.round(page.duration / 1000))
    };
  } catch (error) {
    uni.showToast({
      title: "获取麦克风权限失败",
      icon: 'none',
      duration: 2000,
    })
  }
}

const audioStart__H5 = e => {
  console.log("audioStart__H5", rec);
  if (!rec) return uni.showToast({ title: '未打开录音权限', icon: 'none' });
  rec.start();
  audioLoading.value = true;
  emit("hideTabbar", true)
  audioLoadingRef.value.show()
  clearTimeout(audioTimer.value);
  audioTimer.value = setTimeout(() => {
    audioEnd__H5()
  }, audioTimeLimit.value * 1000);
}

/*语音录制结束*/
function audioEnd() {
  // #ifdef MP-WEIXIN
  audioEnd__WX()
  // #endif
  // #ifndef MP-WEIXIN
  audioEnd__H5New()
  // #endif
}

const audioEnd__WX = () => {
  recorderManager.stop();
  clearTimeout(audioTimer.value);
  audioLoading.value = false;
  audioLoadingRef.value.close()
  emit("hideTabbar", false)
}

const audioEnd__H5New = () => {
  page.duration = audioTime.value;
  clearTimeout(audioTimer.value);
  audioLoading.value = false;
  audioLoadingRef.value.close()
  emit("hideTabbar", false)
  if (page.mediaRecorder) page.mediaRecorder.stop();
}

const audioEnd__H5 = () => {
  console.log("audioEnd__H5");
  clearTimeout(audioTimer.value);
  audioLoading.value = false;
  audioLoadingRef.value.close()
  emit("hideTabbar", false)
  rec.stop((blob, duration) => {
    rec.close();
    console.log("blob", blob);
    uploadAudio__H5(blob, Math.round(duration / 1000))
    audioInit__H5()
  }, msg => {
    rec.close();
    console.log('停止录音失败: ' + msg);
    audioInit__H5()
  });
};

function uploadAudio__WX(tempFilePath, time) {
  uni.uploadFile({
    url: proxy.ipConfig.baseUrl2 + "/common/uploadAudio",
    filePath: tempFilePath,
    name: "file",
    header: {
      Token: uni.getStorageSync("user").token,
    },
    file: tempFilePath,
    formData: {
      time: time
    },
    success: (res) => {
      if (res.statusCode != 200) {
        uni.showToast({
          title: res.errMsg,
          icon: 'none',
          duration: 2000,
        })
      } else if (JSON.parse(res.data).code != 2000) {
        uni.showToast({
          title: res.message,
          icon: 'none',
          duration: 2000,
        })
      } else {
        let resData = JSON.parse(res.data);
        // alert(resData.message);
        console.log("上传成功：", res);
        emit("postAudio", resData.data)
      }
    },
    fail: (err) => {
      // alert("上传失败" + err);
      console.error("上传失败：", err);
    },
  });
}

function uploadAudio__H5(audioBlob, time) {
  // alert("uploadAudio__H5")
  // uni.showToast({
  //   title: "uploadAudio__H5",
  //   icon: 'none',
  //   duration: 2000,
  // })
  const fileData = new FormData();
  fileData.append("audio", audioBlob, "recorded_audio.wav");

  uni.uploadFile({
    url: proxy.ipConfig.baseUrl2 + "/common/uploadAudio",
    filePath: URL.createObjectURL(audioBlob),
    name: "file",
    header: {
      Token: uni.getStorageSync("user").token,
    },
    file: fileData,
    formData: {
      time: time
    },
    success: (res) => {
      if (res.statusCode != 200) {
        uni.showToast({
          title: res.errMsg,
          icon: 'none',
          duration: 2000,
        })
      } else if (JSON.parse(res.data).code != 2000) {
        uni.showToast({
          title: res.message,
          icon: 'none',
          duration: 2000,
        })
      } else {
        let resData = JSON.parse(res.data);
        console.log("上传成功：", res);
        emit("postAudio", resData.data)
      }
    },
    fail: (err) => {
      uni.showToast({
        title: err,
        icon: 'none',
        duration: 2000,
      })
      console.error("上传失败：", err);
    },
  });
}
</script>

<style lang="scss" scoped>
.audioBtn {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFang SC;
  font-size: 32rpx;
  color: #068324;
  background: #FFFFFF;
  border: 2rpx solid #068324;
  margin-bottom: 20rpx;
  margin-top: 8rpx;

  .audioIcon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
}
</style>