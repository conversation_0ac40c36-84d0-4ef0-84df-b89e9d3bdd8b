<template>
  <view class="bottom-box">
    <view class="botton" @click="gotoIndex">
      <image
        v-if="activeName === 'index'"
        :src="bgList.toolbar1hover"
        class="image"
      ></image>
      <image
        v-if="activeName !== 'index'"
        :src="bgList.toolbar1"
        class="image"
      ></image>
      <view
        :class="{
          text1: activeName === 'index',
          text2: activeName !== 'index',
        }"
        >首页</view
      >
    </view>
    <template v-if="yhlx == 2">
      <view class="botton" @click="gotoRegister">
        <image
          v-if="activeName === 'register'"
          :src="bgList.toolbar4hover"
          class="image"
        ></image>
        <image
          v-if="activeName !== 'register'"
          :src="bgList.toolbar4"
          class="image"
        ></image>
        <view
          :class="{
            text1: activeName === 'register',
            text2: activeName !== 'register',
          }"
          >需求登记</view
        >
      </view>
    </template>
    <template v-else>
      <view class="botton" @click="gotoInformation">
        <image
          v-if="activeName === 'information'"
          :src="bgList.toolbar2hover"
          class="image"
        ></image>
        <image
          v-if="activeName !== 'information'"
          :src="bgList.toolbar2"
          class="image"
        ></image>
        <view
          :class="{
            text1: activeName === 'information',
            text2: activeName !== 'information',
          }"
          >苏渔电商</view
        >
      </view>
      <view class="botton" @click="gotoTrading">
        <view class="button-cart">
          <image
            v-if="activeName === 'trading'"
            :src="bgList.toolbar3hover"
            class="image"
          ></image>
          <image
            v-if="activeName !== 'trading'"
            :src="bgList.toolbar3"
            class="image"
          ></image>
          <template v-if="goodsNum || selectGoods">
            <view
              class="cart-num cart-short-num"
              v-if="(Number(goodsNum) || Number(selectGoods)) > 99"
              >99+</view
            >
            <view class="cart-num" v-else>{{ goodsNum || selectGoods }}</view>
          </template>
        </view>
        <view
          :class="{
            text1: activeName === 'trading',
            text2: activeName !== 'trading',
          }"
          style="margin: 0"
          >购物车</view
        >
      </view>
    </template>
    <view class="botton" @click="gotoMy">
      <image
        v-if="activeName === 'my'"
        :src="bgList.toolbar5hover"
        class="image"
        style="width: 32rpx; height: 40rpx"
      >
      </image>
      <image
        v-if="activeName !== 'my'"
        :src="bgList.toolbar5"
        class="image"
        style="width: 32rpx; height: 40rpx"
      >
      </image>
      <view :class="{ text1: activeName === 'my', text2: activeName !== 'my' }"
        >我的</view
      >
    </view>
  </view>
  <up-toast ref="uToastRef"></up-toast>
</template>

<script setup>
import { reactive, getCurrentInstance } from "vue";
import { ref, onMounted } from "vue";
const { proxy } = getCurrentInstance();

const config = proxy.ipConfig;

const bgList = reactive({
  toolbar1hover: config.imageUrl + "/toolbar1hover.png",
  toolbar1: config.imageUrl + "/toolbar1.png",
  toolbar2hover: config.imageUrl + "/toolbar2hover.png",
  toolbar2: config.imageUrl + "/toolbar2.png",
  toolbar3hover: config.imageUrl + "/toolbar3hover.png",
  toolbar3: config.imageUrl + "/toolbar3.png",
  toolbar4hover: config.imageUrl + "/toolbar4hover.png",
  toolbar4: config.imageUrl + "/toolbar4.png",
  toolbar5hover: config.imageUrl + "/toolbar5hover.png",
  toolbar5: config.imageUrl + "/toolbar5.png",
});

const props = defineProps({
  activeName: String,
  goodsNum: {
    type: Number,
    default: 0,
  },
});
const selectGoods = ref(0);
let yhlx = ref(uni.getStorageSync('user')?.yhlx || 1); // 1养殖户 2商户
onMounted(() => {
  yhlx.value = uni.getStorageSync("user")?.yhlx || 1;
  if (
    uni.getStorageSync("selectGoods") &&
    JSON.parse(uni.getStorageSync("selectGoods"))
  ) {
    selectGoods.value = JSON.parse(uni.getStorageSync("selectGoods")).length;
  }
});
const emit = defineEmits(["beforeClick"]);
// 跳转到首页
function gotoIndex() {
  uni.setStorageSync("activeName", JSON.stringify(props.activeName));
  emit("beforeClick", {
    name: "index",
  });
  if (props.activeName === "index") {
    // 什么也不做
  } else {
    uni.redirectTo({
      url: "/pages/indexView/index",
    });
  }
}
// 需求登记
function gotoRegister() {
  uni.setStorageSync("activeName", JSON.stringify(props.activeName));
  let token = uni.getStorageSync("user")?.token;
  if (!token) {
    uToastRef.value.show({
      type: "default",
      icon: false,
      title: "默认主题",
      message: "请登录后操作",
    });
    setTimeout(() => {
      uni.navigateTo({
        url: "/pages/loginView/index",
      });
    }, 500);

    return;
  }
  emit("beforeClick", {
    name: "register",
  });
  if (props.activeName === "register") {
    // 什么也不做
  } else {
    uni.reLaunch({
      url: "/pagesPackages6/demandRegisterView/index",
    });
  }
}
// 跳转到商城
function gotoInformation() {
  uni.setStorageSync("activeName", JSON.stringify(props.activeName));
  emit("beforeClick", {
    name: "information",
  });
  if (props.activeName === "information") {
    // 什么也不做
  } else {
    uni.redirectTo({
      url: "/pagesPackages4/shopView/index",
    });
  }
}
const uToastRef = ref(null);
// 跳转到购物车
function gotoTrading() {
  uni.setStorageSync("activeName", JSON.stringify(props.activeName));
  let token = uni.getStorageSync("user")?.token;
  if (!token) {
    uToastRef.value.show({
      type: "default",
      icon: false,
      title: "默认主题",
      message: "请登录后操作",
      iconUrl: "https://uview-plus.jiangruyi.com/resources/toast/default.png",
    });
    setTimeout(() => {
      uni.navigateTo({
        url: "/pages/loginView/index?activeName=trading",
      });
    }, 500);
    return;
  }
  // selectGoods.value
  let selectGoods = JSON.parse(uni.getStorageSync("selectGoods"));
  uni.setStorageSync("selectFromFoot", JSON.stringify(selectGoods));
  uni.redirectTo({
    url: "/pagesPackages2/shopCartView/index",
  });
}
// 跳转到我的
function gotoMy() {
  uni.setStorageSync("activeName", JSON.stringify(props.activeName));
  let token = uni.getStorageSync("user")?.token;
  if (!token) {
    uToastRef.value.show({
      type: "default",
      icon: false,
      title: "默认主题",
      message: "请登录后操作",
    });
    setTimeout(() => {
      uni.navigateTo({
        url: "/pages/loginView/index",
      });
    }, 500);

    return;
  }
  emit("beforeClick", {
    name: "my",
  });
  if (props.activeName === "my") {
    // 什么也不做
  } else {
    uni.redirectTo({
      url: "/pagesPackages3/myPageView/index",
    });
    return;
  }
}
</script>

<style lang="scss" scoped>
.bottom-box {
  // width: calc(100% - 48rpx);
  height: 180rpx;
  position: fixed;
  left: 0rpx;
  right: 0rpx;
  bottom: 0rpx;
  background-color: #fff;
  // box-shadow: 1rpx 2rpx 10rpx 0rpx rgba(0, 0, 0, 0.2);
  border: 1px solid #dddddd;
  border-radius: 30rpx 30rpx 0 0;
  display: flex;
  // border-radius: 60rpx;
  margin: auto;
  z-index: 999;

  &::after {
    position: fixed;
    content: "";
    left: 0;
    right: 0;
    bottom: 60rpx;
    height: 1px;
    // background-color: #DDDDDD;
    transform: scaleY(0.5);
    transform-origin: 0 0;
  }

  .button-cart {
    position: relative;
  }

  .botton {
    width: 100%;
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    padding-top: 22rpx;
    align-items: center;

    .image {
      width: 40rpx;
      height: 40rpx;
    }

    .text1 {
      width: 150rpx;
      height: auto;
      text-align: center;
      margin-top: 10rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: #068324;
    }

    .text2 {
      width: 150rpx;
      height: auto;
      text-align: center;
      margin-top: 10rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: #333333;
    }

    .cart-num {
      position: absolute;
      top: -16rpx;
      left: 30rpx;
      min-width: 30rpx;
      min-height: 30rpx;
      width: 30rpx;
      height: 30rpx;
      border-radius: 150rpx;
      background: #fa5151;
      font-size: 24rpx;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: PingFang SC;
    }

    .cart-short-num {
      padding: 0 10rpx;
    }
  }
}
</style>
