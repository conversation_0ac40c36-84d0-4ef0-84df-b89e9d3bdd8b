<template>
  <!-- 帮助与反馈 -->
  <view class="content">
    <!-- 顶部 -->
    <view class="navigationBar">
      <view class="navigationBarback"> </view>
      <view class="navigationBarTitleText">
        <!-- 我的 -->
      </view>
      <view class="navigationBarTitleall"> </view>
    </view>
    <!-- 帮助反馈文字说明 -->
    <view class="mainText">
      <view class="title">反馈描述</view>
      <view class="textarea">
        <textarea
          maxlength="300"
          border="none"
          v-model="fknr"
          placeholder="说说您的建议或问题，以便我们提供更好的服务（5个字以上）"
          count
          style="text-align: left; height: 180rpx; width: 100%"
        ></textarea>
      </view>
      <view class="uploadf">
        <!-- <up-upload
          :fileList="fileList1"
          @afterRead="afterRead"
          @delete="deletePic"
          name="1"
          multiple
          :maxCount="10"
        ></up-upload> -->
        <view class="file">
          <view class="upload" @click="afterRead()">
            <image
              :src="config.imageUrl + '/mallOrder/upload.svg'"
              class="upload-img"
            ></image>
          </view>
          <view class="fileItem" v-for="(item, index) in fileList" :key="index">
            <view class="fileItemImg">
              <image
                class="up-avatarImage"
                @click="previewImage(fileList, index)"
                :src="item.allPath"
              ></image>
            </view>
            <view class="delImg" @click="delImage(item, index)">
              <up-icon name="close" color="red" size="14"></up-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <view class="mainBtn">
    <view class="submit" @click="submit"> 提交反馈 </view>
  </view>
  <up-toast ref="uToastRef"></up-toast>
  <up-notify message="功能开发中..." ref="uNotifyRef"></up-notify>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
const uToastRef = ref(null);
const uNotifyRef = ref(null);
let fknr = ref(""); //反馈内容
let fileList = ref([]); //附件列表
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";

var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl;
}

// 删除图片
const deletePic = (event) => {
  this[`fileList${event.name}`].splice(event.index, 1);
};
// 新增图片
const afterRead2 = async (event) => {
  // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
  let lists = [].concat(event.file);
  let fileListLen = this[`fileList${event.name}`].length;
  lists.map((item) => {
    this[`fileList${event.name}`].push({
      ...item,
      status: "uploading",
      message: "上传中",
    });
  });
  for (let i = 0; i < lists.length; i++) {
    const result = await this.uploadFilePromise(lists[i].url);
    let item = this[`fileList${event.name}`][fileListLen];
    this[`fileList${event.name}`].splice(
      fileListLen,
      1,
      Object.assign(item, {
        status: "success",
        message: "",
        url: result,
      })
    );
    fileListLen++;
  }
};
const uploadFilePromise = (url) => {
  return new Promise((resolve, reject) => {
    let a = uni.uploadFile({
      url: "http://************:7001/upload", // 仅为示例，非真实的接口地址
      filePath: url,
      name: "file",
      formData: {
        user: "test",
      },
      success: (res) => {
        setTimeout(() => {
          resolve(res.data.data);
        }, 1000);
      },
    });
  });
};
// new-------------
// 附件上传后的操作
function afterRead() {
  // 获取当前商品已上传图片数量
  const currentCount = fileList.value.length || 0;
  if (currentCount >= 9) {
    uToastRef.value.show({ type: "error", message: "最多上传9张凭证图片" });
    return;
  }
  uni.chooseImage({
    count: 9 - currentCount, //
    sizeType: ["original", "compressed"], // 可以指定是原图还是压缩图，默认二者都有
    sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有
    success: (res) => {
      // 二次校验防止异步操作导致超额
      if (fileList.value.length + res.tempFiles.length > 9) {
        uToastRef.value.show({ type: "error", message: "超出最大上传数量" });
        return;
      }
      res.tempFiles.forEach((file, index) => {
        // 使用条件编译处理不同平台的滚动
        // #ifdef H5
        uni.uploadFile({
          url: proxy.ipConfig.baseUrl2 + "/common/upload",
          file: file,
          name: "files",
          formData: {},
          header: {
            Token: uni.getStorageSync("user").token,
            // 'content-type': 'multipart/form-data'
          },
          success: (uploadFileRes) => {
            let obj = JSON.parse(uploadFileRes.data);
            if (obj.code === 2000) {
              fileList.value.push(obj.data[0]);
            }
          },
        });
        // #endif
        // #ifdef MP-WEIXIN
        uni.uploadFile({
          url: proxy.ipConfig.baseUrl2 + "/common/upload",
          // file: file,
          filePath: file.path,
          name: "files",
          formData: {},
          header: {
            Token: uni.getStorageSync("user").token,
            // 'content-type': 'multipart/form-data'
          },
          success: (uploadFileRes) => {
            let obj = JSON.parse(uploadFileRes.data);
            if (obj.code === 2000) {
              fileList.value.push(obj.data[0]);
            }
          },
          fail: (err) => {},
        });
        // #endif
      });
    },
  });
}
const previewImage = (urls, currentIndex) => {
  uni.previewImage({
    urls: urls.map((item) => item.allPath),
    current: urls[currentIndex].allPath,
  });
};
// 删除图片
const delImage = (file, index) => {
  uni.showModal({
    title: "删除确认",
    confirmColor: "#068324", // 确认按钮颜色
    content: "确定要删除这张凭证图片吗？",
    success: (res) => {
      if (res.confirm) {
        fileList.value.splice(index, 1);
        uToastRef.value.show({ type: "success", message: "删除成功" });
      }
    },
  });
};
// 提交反馈
const submit = () => {
  if (fknr.value.length < 5) {
    uToastRef.value.show({
      type: "error",
      message: "请输入5个字以上的反馈内容",
    });
    return;
  }
  if (fileList.value.length === 0) {
    uToastRef.value.show({ type: "error", message: "请上传凭证图片" });
  }
  let data = {
    fknr: fknr.value,
    fileList: fileList.value,
  };
  console.log(data);
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/grzx/yjfk/savaOpinion",
      {
        ...data,
      },
      {
        method: "POST",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        uToastRef.value.show({ type: "success", message: "提交成功" });
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
      // 返回
      setTimeout(() => {
        uni.navigateBack({ delta: 1 });
      }, 1000);
    });
};
</script>

<style scoped>
.content {
  display: flex;
  align-items: center;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: wrap;
  position: relative;
  width: 100vw;
  height: calc(100vh - 88rpx);
  background-color: #f3f3f3;
}

.navigationBar {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  justify-content: space-between;
  position: fixed;
  left: 0;
  top: 0;
}

.navigationBarTitleText {
  height: 100%;
  opacity: 1;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.mainText {
  box-sizing: border-box;
  width: 700rpx;
  min-height: 400rpx;
  text-align: center;
  line-height: 48rpx;
  background-color: #fff;
  border-radius: 20rpx;
  margin-top: 20rpx;
  padding: 24rpx;
}

.mainText .title {
  width: 100%;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  text-align: left;
}
.mainText .textarea {
  width: 100%;
  height: 180rpx;
}
.mainBtn {
  width: 100%;
  height: 132rpx;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: space-around;
  position: fixed;
  left: 0;
  bottom: 0rpx;
  background-color: #fff;
  padding-bottom: 10rpx;
}

.mainBtn .submit {
  box-sizing: border-box;
  width: 702rpx;
  height: 80rpx;
  border-radius: 306rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  color: #fff;
  font-size: 32rpx;
  background: #068324;
}
:deep(.uni-textarea-textarea) {
  /* text-align: left !important; */
}
/* new */
.mainText .file {
  width: 702rpx;
  display: flex;
  flex-wrap: wrap;
  margin-top: 32rpx;
}
.mainText .upload {
  width: 156rpx;
  height: 156rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.mainText .upload .upload-img {
  width: 100%;
  height: 100%;
}
.mainText .fileItem {
  width: 156rpx;
  height: 156rpx;
  margin-right: 20rpx;
}
.mainText .up-avatarImage {
  width: 156rpx;
  height: 156rpx;
}
.uploadf .fileItem {
  width: 140rpx;
  height: 140rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  position: relative;
}
.delImg {
  /* width: 40rpx;
  height: 40rpx; */
  position: absolute;
  right: 3rpx;
  top: 3rpx;
  display: flex;
  align-items: center;
  justify-items: flex-end;
}
</style>
