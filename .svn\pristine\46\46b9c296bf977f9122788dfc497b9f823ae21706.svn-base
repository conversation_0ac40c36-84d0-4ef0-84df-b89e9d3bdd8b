<template>
  <view class="content">
    <view class="text-area">
      {{ waveDataCount }}
    </view>
    <view class="text-area">
      {{ waveData }}
    </view>
    <view class="text-area">
      <canvas id="canvasWave" width="800" height="200"></canvas>
    </view>
    <view class="text-area">
      <up-button text="开始录音" :disabled="page.isRecording" @click="startRecording"></up-button>
      <up-button text="停止录音" :disabled="!page.isRecording" @click="stopRecording"></up-button>
      <text>录音状态：{{ page.isRecording ? "录音中" : "未录音" }}</text>
      <up-button text="播放录音" @click="playAudio"></up-button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";

// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";

const waveDataCount = ref(0);
const waveData = ref(null);

var page = reactive({
  isRecording: false,
  mediaRecorder: null,
  audioChunks: [],
  audioBlob: null,
});

async function startRecording() {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    // 录音
    page.mediaRecorder = new MediaRecorder(stream);
    page.mediaRecorder.ondataavailable = (event) => {
      page.audioChunks.push(event.data);
    };
    page.mediaRecorder.onstop = () => {
      page.audioBlob = new Blob(page.audioChunks, { type: "audio/mpeg" });
      // convertToBase64();
      uploadAudio();
    };
    page.mediaRecorder.start();
    page.isRecording = true;

    // // 显示波纹
    // const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    // const source = audioContext.createMediaStreamSource(stream);
    // const analyser = audioContext.createAnalyser();
    // source.connect(analyser);
    // analyser.fftSize = 2048;
    // const bufferLength = analyser.frequencyBinCount;
    // const dataArray = new Uint8Array(bufferLength);
    // drawWaveform(analyser, dataArray);
  } catch (error) {
    console.error("获取麦克风权限失败：", error);
  }
}

function stopRecording() {
  if (page.mediaRecorder) {
    page.mediaRecorder.stop();
    page.isRecording = false;
  }
}

function uploadAudio() {
  const formData = new FormData();
  formData.append("audio", page.audioBlob, "recorded_audio.wav");

  uni.uploadFile({
    url: proxy.ipConfig.baseUrl1 + "/audio/upload",
    filePath: URL.createObjectURL(page.audioBlob),
    name: "audio",
    formData: formData,
    success: (res) => {
      alert("上传成功");
      console.log("上传成功：", res);
    },
    fail: (err) => {
      alert("上传失败" + err);
      console.error("上传失败：", err);
    },
  });
}

function uploadAudio2() {
  const formData = new FormData();
  formData.append("audio", page.audioBlob, "recorded_audio.wav");

  uni.uploadFile({
    url: proxy.ipConfig.baseUrl2 + "/common/uploadAudio",
    filePath: URL.createObjectURL(page.audioBlob),
    name: "file",
    header: {
      Token: "9b8c0b1cd7614428802b5e3c574e71be",
    },
    file: formData,
    formData: {
      time: 3000,
    },
    success: (res) => {
      alert("上传成功");
      console.log("上传成功：", res);
    },
    fail: (err) => {
      alert("上传失败" + err);
      console.error("上传失败：", err);
    },
  });
}

function playAudio() {
  // 创建音频实例
  const audioContext = uni.createInnerAudioContext();
  // 配置音频参数
  audioContext.src = "http://*************:9754/jcms/file-1753672486355.MP3"; // 支持本地和远程路径
  audioContext.volume = 1; // 音量范围 0~1
  audioContext.loop = true; // 是否循环播放
  // 事件监听
  audioContext.onPlay(() => console.log("开始播放"));
  audioContext.onError((err) => console.error("播放错误", err));
  // 播放控制
  audioContext.play(); // 开始播放
}

function downloadAudio(audioBlob) {
  const url = URL.createObjectURL(audioBlob);
  const a = document.createElement("a");
  a.style.display = "none";
  a.href = url;
  a.download = "recorded_audio.wav";
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
}

function convertToBase64() {
  const reader = new FileReader();
  reader.readAsDataURL(page.audioBlob);
  reader.onloadend = () => {
    const base64Audio = reader.result;
  };
}

function drawWaveform(analyser, dataArray) {
  // const canvas = document.querySelector("canvasWave");
  const canvas = document.getElementsByClassName("uni-canvas-canvas")[0];
  const ctx = canvas.getContext("2d");
  const draw = () => {
    requestAnimationFrame(draw);
    analyser.getByteTimeDomainData(dataArray);
    waveDataCount.value = waveDataCount.value + 1;
    waveData.value = JSON.stringify(dataArray);
    ctx.fillStyle = "black";
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.lineWidth = 2;
    ctx.strokeStyle = "white";
    ctx.beginPath();
    const sliceWidth = (canvas.width * 1.0) / dataArray.length;
    let x = 0;
    for (let i = 0; i < dataArray.length; i++) {
      const v = dataArray[i] / 32.0;
      const y = (v * canvas.height) / 2;
      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
      x += sliceWidth;
    }
    ctx.lineTo(canvas.width, canvas.height / 2);
    ctx.stroke();
  };

  draw();
}
</script>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-top: 200rpx;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50rpx;
}

.text-area {
  width: 500rpx;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* align-items: center; */
  margin-bottom: 20rpx;
}

.label {
  font-size: 36rpx;
  font-weight: bold;
}

.title {
  font-size: 30rpx;
  color: rgb(48, 49, 51);
}
</style>
