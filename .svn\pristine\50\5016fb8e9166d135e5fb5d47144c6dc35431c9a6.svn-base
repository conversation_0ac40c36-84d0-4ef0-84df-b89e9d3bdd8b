<template>
  <view :style="`padding-top:${titleHeight}px;position:relative`">
    <!-- 顶部 -->
    <view
      class="navigationBar"
      :style="`height:${searchBarHeight}px;top:${titleHeight}px`"
    >
      <view class="navigationBarback" @click="goTomyPageView()">
        <up-icon name="arrow-left" size="42rpx" color="#000000"></up-icon>
      </view>
      <view class="navigationBarTitleText"> 卖鱼订单 </view>
      <view class="navigationBarTitleall"> </view>
    </view>
    <!-- <up-sticky bgColor="#fff" :offsetTop="searchBarHeight"> -->
    <view class="sticky" :style="`top:${searchBarHeight + titleHeight}px`">
      <up-tabs
        :list="tabList"
        :scrollable="false"
        lineColor="#068324"
        lineHeight="8rpx"
        :activeStyle="{ color: '#333333' }"
        :inactiveStyle="{ color: '#666' }"
        @click="tabClick"
        :current="currentIndex"
      ></up-tabs>
    </view>

    <!-- </up-sticky> -->

    <view
      class="orderList"
      v-if="page.orderList.length > 0"
      :style="`margin-top:${searchBarHeight * 2}px;height:calc(100vh - ${
        searchBarHeight * 3
      }px);`"
    >
      <view class="orderItem" v-for="item in page.orderList" :key="item.id">
        <view class="orderItem_title">
          <view
            class="title_status"
            :class="[
              item.status == '4'
                ? 'status1'
                : item.status == '0'
                ? 'status2'
                : 'status3',
            ]"
          >
            {{ changeStatuslabel(item.status) }}
          </view>
          <view class="title_name">{{ item.category }}</view>
          <view class="title_id">{{ item.code }}</view>
        </view>
        <view class="orderItem_content">
          <view class="content_info">
            <view class="content_item" v-if="1">
              <view class="title">成交单价(元/斤)</view>
              <view class="price">{{ item.lastPrice }}</view>
            </view>
            <!-- <view class="content_item" v-else>
              <view class="title">采购报价(元/斤)</view>
              <view class="price">{{ item.lastPrice }}</view>
            </view> -->
            <view class="content_item">
              <view class="title">采购报价(元/斤)</view>
              {{ item.originalPrice }}
            </view>
            <view class="content_item">
              <view class="title">收鱼日期</view>
              {{ item.acceptTime }}
            </view>
          </view>
          <!-- <view class="content_remind">
            <image class="remindIcon" :src="config.imageUrl + '/shop/remind.png'"></image>
            有商品待收货，请及时收货
          </view> -->
          <view class="content_btn">
            <template v-if="item.status == '0'">
              <view class="btnItem lightBtn" @click="gotoDetails(item)"
                >查看订单</view
              >
              <view class="btnItem lightBtn" @click="gotoConfirmShipment(item)"
                >确认出货</view
              >
            </template>
            <template v-else-if="item.status == '1'">
              <view class="btnItem" @click="gotoDetails(item)">查看订单</view>
              <view class="btnItem" @click="gotoShipmentinfomation(item)"
                >出货信息</view
              >
            </template>
            <template v-else-if="item.status == '2'">
              <view class="btnItem" @click="gotoDetails(item)">查看订单</view>
              <view class="btnItem" @click="gotoShipmentinfomation(item)"
                >出货信息</view
              >
              <view
                class="btnItem lightBtn"
                @click="gotoSettlementCenterdetails(item)"
                >结算信息</view
              >
            </template>
            <template v-else-if="item.status == '3'">
              <view class="btnItem" @click="gotoDetails(item)">查看订单</view>
            </template>
          </view>
        </view>
      </view>
    </view>
    <NoData
      v-if="page.orderList.length == 0"
      :imgStyleObj="imgStyleObj"
      :nodataTextTitle="nodataTextTitle"
    ></NoData>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";

// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";
const config = proxy.ipConfig;
import eventBus from "@/http/eventBus.js";

import NoData from "../components/noData.vue";
//缺省图
let nodataTextTitle = ref("暂无订单");
let imgStyleObj = ref({
  width: "400rpx",
  height: "400rpx",
});
const currentIndex = ref(0);
const tabList = ref([
  { id: 0, name: "全部", value: "" },
  { id: 1, name: "待出货", value: "0" },
  { id: 2, name: "待收货", value: "1" },
  { id: 3, name: "已完成", value: "2" },
  { id: 4, name: "已取消", value: "3" },
]);
const page = reactive({
  orderList: [],
  page: 1, // 当前页码
  pageSize: 10, // 每页显示的数量
  total: 0, // 总记录数
  status: "",
});

onShow(() => {
  uni.setNavigationBarTitle({ title: "我要卖鱼" });
  eventBus.$on("haveNewMsg", () => {
    page.page = 1;
    page.orderList = [];
    getFarmersData();
  });
});
onHide(() => {
  eventBus.$off("haveNewMsg");
});
onUnload(() => {
  eventBus.$off("haveNewMsg");
});
let statusList = ref([]); // 状态列表
function tabClick(item) {
  console.log("tabClick", item);
  page.status = item.value;
  getFarmersData();
}
// 议价
function gotoBargain(item) {
  uni.navigateTo({ url: "/pagesPackages6/demandShView/bargain" });
}
// 详情
function gotoDemandDetail(item) {
  uni.navigateTo({ url: "/pagesPackages6/demandShView/detail" });
}
// 跳转详情/
function gotoDetails(item) {
  uni.navigateTo({
    url: "/pagesPackages3/fishOrderView/detail?orderId=" + item.orderId,
  });
}
//跳转确认出货
function gotoConfirmShipment(item) {
  //  判断是否有默认地址
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/grzx/qtfw/getDefaultAddr",
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        if (data && data.uaddressId && data.defaultStatus == 1) {
          uni.navigateTo({
            url:
              "/pagesPackages3/fishOrderView/confirmShipment?orderId=" +
              item.orderId +
              "&dpId=" +
              item.dpId +
              "&demandId=" +
              item.demandId,
          });
        } else {
          uni.showModal({
            title: "添加地址",
            content: "请添加起渔地址,并设置成默认地址!",
            success: (res) => {
              if (res.confirm) {
                uni.navigateTo({
                  url: "/pagesPackages2/receiveAddressView/components/addAddress?type=1&id=",
                });
              }
            },
          });
        }
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
}
//跳转出货信息
function gotoShipmentinfomation(item) {
  uni.navigateTo({
    url:
      "/pagesPackages3/fishOrderView/shipmentInformation?orderId=" +
      item.orderId,
  });
}
//跳转结算详情
function gotoSettlementCenterdetails(item) {
  uni.navigateTo({
    url: "/pagesPackages3/settlementCenterView/details?orderId=" + item.orderId,
  });
}
let producerId = ref("");
onLoad((options) => {
  if (options && options.status != null) {
    page.status = options.status;
    if (options.status == "") {
      currentIndex.value = 0;
    } else {
      currentIndex.value = Number(options.status) + 1;
    }
  }
  let user = uni.getStorageSync("user");
  producerId.value = user.memberId;
  getFarmersData();
});

//我要卖鱼列表
const getFarmersData = () => {
  let params = {
    page: 1,
    size: 10,
    status: page.status,
  };
  http
    .post(proxy.ipConfig.baseUrl2 + "/farmersHome/myOrderList", params, {
      method: "GET",
    })
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        page.orderList = data.records;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
// 状态转换
const changeStatuslabel = (status) => {
  // 0：待出货，1：待收货，2：已完成，3：已取消 4:待接单

  if (status == 0) {
    return "待出货";
  }
  if (status == 1) {
    return "待收货";
  }
  if (status == 2) {
    return "已完成";
  }
  if (status == 3) {
    return "已取消";
  }
  if (status == 4) {
    return "待接单";
  }
};
// 获取微信右上角胶囊高度
let titleHeight = ref(0);
let searchBarHeight = ref(44);
const getHeight = () => {
  // #ifdef H5
  // #endif
  // #ifdef MP-WEIXIN
  let res = wx.getMenuButtonBoundingClientRect();
  titleHeight.value = res.top; //获取距离赋值给
  searchBarHeight.value = res.height;
  console.log("🚀 ~ getHeight ~ searchBarHeight:", searchBarHeight.value);
  // #endif
};
onMounted(() => {
  getHeight();
});
const goTomyPageView = () => {
  // 返回的商店页面
  uni.navigateTo({ url: "/pagesPackages3/myPageView/index" }); // 关闭所有页面并打开目标页
  return;
};
</script>

<style lang="scss" scoped>
.orderList {
  padding: 24rpx;
  overflow: auto;
  .orderItem {
    padding: 0 24rpx;
    background-color: #fff;
    margin-bottom: 24rpx;
    border-radius: 20rpx;

    .orderItem_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      font-family: PingFang SC;
      font-size: 24rpx;
      font-weight: normal;

      .title_id {
        color: #666666;
      }

      .title_name {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
        margin: 0 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .title_status {
        height: 50rpx;
        line-height: 50rpx;
        border-radius: 20rpx 0;
        padding: 0 10rpx;
      }

      .status1 {
        color: #267fff;
        background: rgba(38, 127, 255, 0.1);
      }

      .status2 {
        color: #fa5151;
        background: rgba(250, 81, 81, 0.1);
      }

      .status3 {
        color: #ff8d02;
        background: rgba(255, 141, 2, 0.1);
      }
    }

    .orderItem_content {
      padding-bottom: 24rpx;
      // padding: 10rpx 0 14rpx;

      .content_info {
        display: flex;
        border-radius: 20rpx;
        border: 2rpx solid #eaeaea;
        padding: 24rpx;
      }

      .content_item {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
        border-right: 2rpx solid #eaeaea;
        text-align: center;
        padding: 0 15rpx;

        &:first-child {
          padding-left: 0;
        }

        &:last-child {
          border-right: unset;
          // flex: unset;
          width: 150rpx;
          padding-right: 0;
        }

        .title {
          color: #666666;
          margin-bottom: 16rpx;
          font-size: 24rpx;
        }

        .price {
          color: #ff0000;
        }
      }

      .content_remind {
        display: flex;
        align-items: center;
        padding: 12rpx 8rpx;
        border-radius: 8rpx;
        gap: 10px;
        background: #f6f6f6;
        font-family: PingFang SC;
        font-size: 24rpx;
        color: #ff0000;

        .remindIcon {
          width: 28rpx;
          height: 28rpx;
        }
      }

      .content_btn {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 24rpx;
        gap: 14rpx;

        .btnItem {
          width: 148rpx;
          border-radius: 10rpx;
          padding: 10rpx 0;
          background: #f4f4f4;
          text-align: center;
          font-family: PingFang SC;
          font-size: 24rpx;
          color: #333333;
        }

        .lightBtn {
          background: #effff3;
          color: #068324;
        }
      }
    }
  }
}
/* //顶部自定义 */
.navigationBar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  position: fixed;
  left: 0;
  top: 0px;
  padding: 14rpx 6rpx;
  box-sizing: border-box;
  background-color: #fff;
}
.navigationBarback {
  width: 52rpx;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  z-index: 10;
}
.navigationBarTitleText {
  width: 100%;
  height: 100%;
  opacity: 1;
  font-size: 32rpx;
  /* font-weight: bold; */
  color: #000000;
  position: absolute;
  top: 0;
  left: 0;
  text-align: center;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  background-color: #fff;
}
.sticky {
  width: 100%;
  background-color: #fff;
  // position: absolute;
  // z-index: 999;
  position: fixed;
}
</style>
