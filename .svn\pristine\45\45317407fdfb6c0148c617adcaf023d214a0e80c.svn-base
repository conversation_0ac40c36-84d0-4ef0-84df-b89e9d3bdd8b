<template>
  <view
    class="content"
    :style="'background-image:url(' + config.imageUrl + '/myPage/bgc2.png)'"
  >
    <!-- 顶部 -->
    <view class="navigationBar">
      <view class="navigationBarback"> </view>
      <view class="navigationBarTitleText">
        <!-- 我的 -->
      </view>
      <view class="navigationBarTitleall"> </view>
    </view>
    <!-- 用户信息 -->
    <view class="userinfo">
      <view class="avatarImage">
        <button
          class="avatarImage_btn"
          open-type="chooseAvatar"
          @chooseavatar="onChooseAvatar"
        >
          <image
            class="up-avatarImage"
            width="100%"
            height="100%"
            :src="avatarUrl"
            shape="circle"
          ></image>
        </button>
        <view class="editPhoto">
          <image
            style="width: 20rpx; height: 14rpx"
            :src="config.imageUrl + '/myPage/editPhoto.svg'"
            shape="circle"
          ></image>
        </view>
      </view>
    </view>
    <!-- 修改密码 -->
    <view class="list">
      <view class="phoneNumber">
        当前登录账号 : {{ userinfoData.account }}
      </view>
      <view class="nowname"> 用户名 : {{ userinfoData.nickname }} </view>
      <!-- 注意，如果需要兼容微信小程序，最好通过setRules方法设置rules规则 -->
      <up-form
        labelPosition="left"
        :model="passwordArr"
        :rules="rules"
        ref="uFormRef"
      >
        <up-form-item
          label="新密码"
          prop="password"
          borderBottom
          ref="item1"
          labelWidth="180rpx"
        >
          <up-input v-model="passwordArr.password" border="none"></up-input>
        </up-form-item>
        <up-form-item
          label="确认新密码"
          prop="surepassword"
          borderBottom
          ref="item1"
          labelWidth="180rpx"
        >
          <up-input v-model="passwordArr.surepassword" border="none"></up-input>
        </up-form-item>
      </up-form>
      <view class="psddes">
        密码长度为8-15位，需包括特殊字符（如%、@、&、#等），且需包含大写字母、小写字母、数字中的两种或以上，不允许有空格
      </view>
      <!-- 按钮 -->
      <view class="mainBtn">
        <view class="cacel" @click="cancel"> 返回 </view>
        <view class="submit" @click="submit"> 确定 </view>
      </view>
    </view>
  </view>
  <ndb-foot activeName="my"></ndb-foot>
  <up-code
    ref="uCode"
    @change="codeChange"
    seconds="20"
    @start="disabled1 = true"
    @end="disabled1 = false"
  ></up-code>
  <up-toast ref="uToastRef"></up-toast>
  <up-notify message="功能开发中..." ref="uNotifyRef"></up-notify>
  <up-modal
    title="确定退出登录?"
    :show="showModal"
    @cancel="cancelFn"
    @confirm="confirmFn"
    ref="uModal"
    :asyncClose="true"
    :showCancelButton="true"
  ></up-modal>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
// ------------退出登录
import md5 from "md5";
// 创建响应式数据
const showModal = ref(false);

const confirmFn = () => {
  setTimeout(() => {
    showModal.value = false;
    uni.redirectTo({
      url: "/pages/indexView/index",
    });
  }, 500);
};
// 取消
const cancelFn = () => {
  showModal.value = false;
};

// ------------退出登录
const uToastRef = ref(null);
const uNotifyRef = ref(null);
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

// 导入http
import { http, toast } from "uview-plus";

var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl1;
}

//用户信息
let userinfoData = ref({
  nickname: "",
  account: "",
  memberHeadSource: {},
});
let avatarUrl = ref(config.imageUrl + "/myPage/tx.svg");
//获取用户数据
const getUserinfo = () => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/grzx/grxx/getMemberInfo",
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        userinfoData.value = { ...data };
        if (userinfoData.value.icon) avatarUrl.value = userinfoData.value.icon;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};
//订单信息
getConfig();
getUserinfo(); //用户
// 头像上传
const onChooseAvatar = (e) => {
  avatarUrl.value = e.detail.avatarUrl;
  uploadAvatar(); // 上传头像到服务器获取真实地址
  updateUser();
};
const uploadAvatar = () => {};
//
let passwordArr = ref({
  password: "", //原密码
  surepassword: "", //新密码
});

// 用户更新接口
// 密码校验规则：8-15位，含特殊字符（%@&#），至少两种字符类型（大写/小写/数字），无空格
let rules = reactive({
  password: [
    {
      required: true,
      message: "请输入密码",
      trigger: ["change", "blur"],
    },
    {
      validator: (rule, value, callback) => {
        // 密码校验规则：8-15位，含特殊字符（%@&#），至少两种字符类型（大写/小写/数字），无空格
        if (!value) {
          callback(new Error("请输入密码"));
          return;
        }
        // 校验长度
        if (value.length < 8 || value.length > 15) {
          callback(new Error("密码长度需为8-15位"));
          return;
        }
        // 校验特殊字符（%@&#）
        if (!/[%@&#]/.test(value)) {
          callback(new Error("密码需包含%、@、&、#中的至少一个特殊字符"));
          return;
        }
        // 校验空格
        if (/\s/.test(value)) {
          callback(new Error("密码不能包含空格"));
          return;
        }
        // 校验字符类型（至少两种：大写、小写、数字）
        const hasUpper = /[A-Z]/.test(value);
        const hasLower = /[a-z]/.test(value);
        const hasNumber = /[0-9]/.test(value);
        const typeCount = [hasUpper, hasLower, hasNumber].filter(
          Boolean
        ).length;
        if (typeCount < 2) {
          callback(new Error("密码需包含大写、小写、数字中的至少两种"));
          return;
        }
        // 所有校验通过
        callback();
      },
      message: "密码有误，请按要求格式设置",
      trigger: ["blur"],
    },
    {
      validator: async (rule, value, callback) => {
        // 基础格式校验（长度、特殊字符等）
        // if (value.length < 8 || value.length > 15) {
        //   callback(new Error("密码长度需为8-15位"));
        //   return;
        // }
        // 调用后端校验原密码是否匹配
        try {
          const res = await http.post(
            proxy.ipConfig.baseUrl2 + "/grzx/grxx/validMemberPassword",
            {
              password: md5(md5(value)),
            },
            {
              method: "POST",
            }
          );
          if (res) {
            if (res.data.code == 2000) {
              callback();
            } else {
              uToastRef.value.show({
                type: "error",
                message: res.data.message,
              });
            }
          } else {
            callback(new Error(res));
          }
        } catch (error) {
          callback(new Error("密码校验失败，请重试"));
        }
      },
      trigger: ["blur"],
    },
  ],
  surepassword: [
    {
      required: true,
      message: "请输入密码",
      trigger: ["change", "blur"],
    },
    {
      validator: (rule, value, callback) => {
        // 密码校验规则：8-15位，含特殊字符（%@&#），至少两种字符类型（大写/小写/数字），无空格
        if (!value) {
          callback(new Error("请输入密码"));
          return;
        }
        // 校验长度
        if (value.length < 8 || value.length > 15) {
          callback(new Error("密码长度需为8-15位"));
          return;
        }
        // 校验特殊字符（%@&#）
        if (!/[%@&#]/.test(value)) {
          callback(new Error("密码需包含%、@、&、#中的至少一个特殊字符"));
          return;
        }
        // 校验空格
        if (/\s/.test(value)) {
          callback(new Error("密码不能包含空格"));
          return;
        }
        // 校验字符类型（至少两种：大写、小写、数字）
        const hasUpper = /[A-Z]/.test(value);
        const hasLower = /[a-z]/.test(value);
        const hasNumber = /[0-9]/.test(value);
        const typeCount = [hasUpper, hasLower, hasNumber].filter(
          Boolean
        ).length;
        if (typeCount < 2) {
          callback(new Error("密码需包含大写、小写、数字中的至少两种"));
          return;
        }
        // 所有校验通过
        callback();
      },
      message: "密码有误，请按要求格式设置",
      trigger: ["blur"],
    },
    {
      validator: async (rule, value, callback) => {
        // 修改密码时，两次输入的新密码在满足密码规则情况下要保持一致，不一致时提示：两次输入的密码不一致
        if (value !== passwordArr.value.password) {
          callback(new Error("两次输入的密码不一致"));
          return;
        }
      },
      trigger: ["blur"],
    },
  ],
});

const uFormRef = ref(null);
const disabled1 = ref(false);
const tips = ref("");
const uCode = ref(null);
const getCode = () => {
  if (uCode.value.canGetCode) {
    // 模拟向后端请求验证码
    uni.showLoading({
      title: "正在获取验证码",
    });
    setTimeout(() => {
      uni.hideLoading();
      // 这里此提示会被this.start()方法中的提示覆盖
      uni.$u.toast("验证码已发送");
      // 通知验证码组件内部开始倒计时
      uCode.value.start();
    }, 2000);
  } else {
    uni.$u.toast("倒计时结束后再发送");
  }
};
const codeChange = (text) => {
  tips.value = text;
};
// 取消
const cancel = () => {
  uni.redirectTo({
    url: "/pagesPackages3/personalInfoView/index",
  });
};
// 确定
const submit = () => {
  uFormRef.value
    .validate()
    .then((valid) => {
      if (valid) {
        // 校验通过，执行提交逻辑
        // 提交表单数据到后端
        http
          .post(
            proxy.ipConfig.baseUrl2 + "/grzx/grxx/updateMemberPassword",

            {
              password: md5(md5(passwordArr.value.password)),
            },
            {
              method: "POST",
            }
          )
          .then((res) => {
            if (res.data.code == 2000) {
              let data = res.data.data;
              // 提示语
              // uToastRef.value.show({
              //   type: "success",
              //   message: data,
              // });
              uni.showToast({
                title: data,
                icon: "success",
                duration: 1000,
              });
              // 成功后跳转到个人信息页面
              if (data) {
                uni.redirectTo({
                  url: "/pagesPackages3/personalInfoView/index",
                });
              }
            } else {
              uToastRef.value.show({
                type: "error",
                message: res.data.message,
              });
            }
          });
      } else {
        uni.$u.toast("校验失败");
      }
    })
    .catch(() => {
      // 处理验证错误
      uni.$u.toast("校验失败");
    });
};
</script>

<style scoped>
.content {
  box-sizing: border-box;
  display: flex;
  height: calc(100vh - 88rpx);
  overflow: hidden;
  align-items: flex-start;
  justify-content: center;
  align-content: flex-start;
  flex-wrap: wrap;
  position: relative;
  background-color: #f3f3f3;
  /* background-image: url(/images/myPage/bgc2.png); */
  /* background-image: url(/images/myPage/bgc3.svg); */
  background-size: 838.95rpx 649.19rpx;
  /* background-size: contain; */
  /* 覆盖容器但保持宽高比‌:ml-citation{ref="5" data="citationList"} */
  background-position: center -191.86rpx;
  /* 垂直位置下移25%，显示下半部分‌:ml-citation{ref="4,6" data="citationList"} */
  background-repeat: no-repeat;
}

.navigationBar {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  justify-content: space-between;
  position: fixed;
  left: 0;
  top: 0;
}

.navigationBarTitleText {
  height: 100%;
  opacity: 1;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

/* 用户信息 */
.userinfo {
  width: 700rpx;
  height: 112rpx;
  /* background-color: #008443; */
  border-radius: 3.49rpx;
  box-shadow: 0px 1.74rpx 8.72rpx 0px rgba(202, 204, 204, 0.2);
  font-size: 24.42rpx;
  padding: 0rpx 34.88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 88rpx 0;
}

.avatarImage {
  width: 144rpx;
  height: 144rpx;
  border-radius: 50%;
  border: 1.74rpx solid #fff;
  margin-right: 20.93rpx;
  position: relative;
}

:deep(.avatarImage .up-button),
.avatarImage_btn {
  width: 144rpx;
  height: 144rpx;
  background-color: transparent;
  padding: 0;
  border: none;
}

:deep(.avatarImage .up-button:after),
.avatarImage_btn:after {
  display: none;
}

:deep(.avatarImage .up-button:before),
.avatarImage_btn:before {
  display: none;
}

.up-avatarImage {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.userinfo-right {
  width: 401.16rpx;
  height: 112rpx;
  color: #fff;
}

.name {
  width: 226.74rpx;
  height: 48.84rpx;
  line-height: 48.84rpx;
  border-radius: 47.09rpx;
  /* background-color: rgba(119, 118, 118, 0.5); */
  /* color: #fff; */
  font-size: 32rpx;
  text-align: center;
  /* box-shadow: 0px 1.74rpx 8.72rpx 0px rgba(202, 204, 204, 0.2); */
  color: #333333;
  text-align: left;
}

.phoneNumber,
.nowname {
  width: 100%;
  font-family: PingFang SC;
  line-height: 70rpx;
  font-size: 36rpx;
  font-weight: 500;
  font-variation-settings: "opsz" auto;
  color: #068324;
  text-align: left;
}

.list {
  width: 700rpx;
  min-height: 60vh;
  border-radius: 30rpx;
  background-color: #fff;
  padding: 20rpx;
  box-sizing: border-box;
  position: relative;
}

.editPhoto {
  width: 32rpx;
  height: 32rpx;
  /* 自动局 */
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  box-sizing: border-box;
  border: 1px solid #068324;
  border-radius: 50%;
  position: absolute;
  right: 0;
  bottom: 0;
}
:deep(.up-cell-group__wrapper .up-line:first-of-type) {
  display: none;
}
:deep(.u-form-item) {
  border-bottom: 1px solid rgb(214, 215, 217);
}
.mainBtn {
  width: 100%;
  height: 132rpx;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: space-around;
  position: absolute;
  left: 0;
  bottom: 0rpx;
  background-color: #fff;
  padding-bottom: 10rpx;
}

.mainBtn .submit {
  box-sizing: border-box;
  width: 300rpx;
  height: 80rpx;
  border-radius: 306rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  color: #fff;
  font-size: 32rpx;
  background: #068324;
}
.mainBtn .cacel {
  box-sizing: border-box;
  width: 300rpx;
  height: 80rpx;
  border-radius: 306rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  color: #068324;
  font-size: 32rpx;
  background: #fff;
  border: #068324 solid 2rpx;
}
:deep(.uni-textarea-textarea) {
  text-align: left;
}
.psddes {
  margin-top: 24rpx;
  font-size: 24rpx;
  color: #ff3838;
}
</style>
