<template>
  <up-popup :show="showPopup" mode="bottom" @close="close" :zIndex="99999" :closeOnClickOverlay="false"
    :safeAreaInsetBottom="false" :customStyle="{ background: 'transparent' }">
    <view class="modal-body" v-if="audioLoading && (audioTimeLimit * 1000 - audioTime > 0)">
      <!-- {{ audioTimeLimit }}
      {{ audioTime }}
      <view class="time" v-if="audioTimeLimit * 1000 - audioTime < 3000">
        {{ Math.floor((audioTimeLimit * 1000 - audioTime) / 1000) + 1 }}″ 后自动结束
      </view> -->
      <!-- <view class="time" v-else>{{ formatDateToss(audioTime) }}</view> -->
      <image class="audioCircle" :src="config.imageUrl + '/shop/audioCircle.png'" />
      <view class="sound-waves">
        <view v-for="(item, index) in radomHeight" :key="index"
          :style="`height: ${item}rpx; margin-top: -${item / 2}rpx;`"></view>
        <view style="clear: both; width: 0; height: 0"></view>
      </view>
    </view>
    <view class="endAndSend" @click="audioEnd">发送</view>
  </up-popup>
</template>

<script setup>
import { watch, ref } from 'vue';

import { onLoad } from '@dcloudio/uni-app';
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
const config = proxy.ipConfig

const emit = defineEmits(['audioEnd'])
import dayjs from 'dayjs';

const props = defineProps({
  audioTime: {
    type: Number,
  },
  audioLoading: {
    type: Boolean,
    default: false,
  },
  audioTimeLimit: {
    type: Number,
    default: 60,
  }
});
const radomHeight = ref([
  50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
  50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
  50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
  50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
]);
let showPopup = ref(false);
function show() {
  showPopup.value = true;
}
function close() {
  showPopup.value = false;
}
onLoad(() => { });
let timer;
watch(
  () => props.audioLoading,
  val => {
    if (val) {
      timer = setInterval(() => {
        myradom();
      }, 500);
    } else {
      clearInterval(timer);
    }
  },
);

watch(
  () => props.audioTime,
  val => {
    if (props.audioTimeLimit * 1000 - val < 200) {
      audioEnd()
    }
  },
);

const myradom = () => {
  let _radomheight = radomHeight.value;
  for (var i = 0; i < radomHeight.value.length; i++) {
    //+1是为了避免为0
    _radomheight[i] = 50 * Math.random().toFixed(2) + 10;
  }
  radomHeight.value = _radomheight;
};

function formatDateToss(inputStr) {
  return dayjs(inputStr).format('mm:ss');
}

function audioEnd() {
  close()
  emit("audioEnd")
}
defineExpose({ show });
</script>

<style scoped lang="scss">
.modal-body {
  position: relative;
  width: 100%;
  background: transparent;
  backdrop-filter: blur(20rpx);
  box-sizing: border-box;
  // height: 70rpx;
  padding-bottom: 60rpx;
}

.audioCircle {
  position: absolute;
  z-index: -1;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 218rpx;
}

.endAndSend {
  height: 92rpx;
  line-height: 92rpx;
  text-align: center;
  background: #25A243;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 500;
  color: #FFFFFF;
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom));
}

.time {
  width: 100%;
  text-align: center;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
}

.sound-waves {
  width: 400rpx;
  box-sizing: border-box;
  margin: 70rpx auto 0;
  height: 62rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.sound-waves view {
  transition: all 0.5s;
  width: 4rpx;
  // margin-right: 4rpx;
  height: 100%;
  background-color: #ffffff;
  float: left;
}

.desc {
  width: 100%;
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 42rpx;
  text-align: center;
  margin-top: 20rpx;
}

.record-btn {
  width: 584rpx;
  height: 74rpx;
  line-height: 74rpx;
  text-align: center;
  background: #ffffff;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #000000;
}

.record-btn::after {
  border: none;
}
</style>