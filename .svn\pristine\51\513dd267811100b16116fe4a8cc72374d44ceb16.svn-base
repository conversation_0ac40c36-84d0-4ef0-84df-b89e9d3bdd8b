<template>
  <view class="main">
    <up-sticky bgColor="#fff">
      <up-tabs
        :list="tabList"
        :scrollable="false"
        lineColor="#068324"
        lineHeight="8rpx"
        :activeStyle="{ color: '#333333', fontSize: '36rpx' }"
        :inactiveStyle="{ color: '#666', fontSize: '32rpx' }"
        @click="tabClick"
        :current="currentIndex"
      ></up-tabs>
    </up-sticky>
    <scroll-view
      v-if="showNodata == 'list'"
      scroll-y="true"
      class="scroll-box"
      @scrolltolower="loadMore"
      :style="`height:calc(100vh - 44px)`"
      :lower-threshold="20"
    >
      <view class="orderList" v-if="page.orderList.length">
        <view class="orderItem" v-for="item in page.orderList" :key="item.id">
          <view class="orderItem_title">
            <view
              class="title_status"
              :class="[
                item.status == 0
                  ? 'status0'
                  : item.status == 1
                  ? 'status1'
                  : item.status == 2
                  ? 'status2'
                  : 'status3',
              ]"
            >
              {{ getStatusName(item.status) }}
            </view>
            <view class="title_name">{{ item.productName }}</view>
            <view class="title_id">{{ item.demandCode }}</view>
          </view>
          <view class="orderItem_content">
            <view class="content_info">
              <view
                class="content_item"
                v-if="item.status == 1 && item.cpPrice"
              >
                <view class="title">最新议价(元/斤)</view>
                <view class="price">{{ item.cpPrice }}</view>
              </view>
              <view class="content_item" v-if="item.status == 2">
                <view class="title">成交单价(元/斤)</view>
                <view class="price">{{ item.cpPrice }}</view>
              </view>
              <view class="content_item">
                <view class="title">采购报价(元/斤)</view>
                {{ item.price }}
              </view>
              <view class="content_item">
                <view class="title">收鱼日期</view>
                <!-- {{ getSyTime(item.acceptDate) }} -->
                {{ item.acceptDate }}
              </view>
            </view>
            <!-- <view class="content_remind">
            <image class="remindIcon" :src="config.imageUrl + '/shop/remind.png'"></image>
            有商品待收货，请及时收货
          </view> -->
            <view class="content_btn">
              <template v-if="item.status == 0">
                <view class="btnItem" @click="cancelXq(item)">取消需求</view>
                <view class="btnItem lightBtn" @click="gotoDemandDetail(item)"
                  >查看需求</view
                >
              </template>
              <template v-else-if="item.status == 1">
                <view class="btnItem" @click="cancelXq(item)">取消需求</view>
                <view class="btnItem" @click="gotoDemandDetail(item)"
                  >查看需求</view
                >
                <view class="btnItem" @click="gotoBargain(item)">议价</view>
                <view
                  class="btnItem lightBtn"
                  v-if="item.dfbj"
                  @click="agreeNewPrice(1, item)"
                  >同意新价格</view
                >
              </template>
              <template v-else-if="item.status == 2">
                <view class="btnItem" @click="gotoDemandDetail(item)"
                  >查看需求</view
                >
                <view class="btnItem lightBtn" @click="gotoDdxq(item)"
                  >查看订单</view
                >
              </template>
              <template v-else-if="item.status == 3">
                <view class="btnItem" @click="delSq(item)">删除需求</view>
                <view class="btnItem lightBtn" @click="gotoDemandDetail(item)"
                  >查看需求</view
                >
              </template>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <!-- <u-loadmore v-if="!noData" :status="loadStatus" /> -->
    <nbdNodata
      v-if="showNodata == 'nodata'"
      :imgStyleObj="imgStyleObj"
      :nodataTextTitle="nodataTextTitle"
    ></nbdNodata>
  </view>
  <up-popup v-model:show="showAgreePopup" mode="center" :round="'20rpx'">
    <view class="agreeView">
      <view class="content" v-if="agreeType == 1">
        是否同意新价格<view class="redColor">{{ agreePrice }}元/斤</view>？
      </view>
      <view class="agreeBtn">
        <button @click="cancelClick">取消</button>
        <button @click="confirmClick">确认</button>
      </view>
    </view>
  </up-popup>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";

// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
import nbdNodata from "@/components/ndb-no-data/index.vue";
//缺省图
let nodataTextTitle = ref("暂无数据");
let imgStyleObj = ref({
  width: "400rpx",
  height: "400rpx",
});
// 导入http
import { http, toast } from "uview-plus";
const config = proxy.ipConfig;
import eventBus from "@/http/eventBus.js";

const currentIndex = ref(0);
const tabList = ref([
  { id: 0, name: "全部", value: "" },
  { id: 1, name: "待分配", value: "0" },
  { id: 2, name: "待接单", value: "1" },
  { id: 3, name: "已接单", value: "2" },
  { id: 4, name: "已取消", value: "3" },
]);
const page = reactive({
  orderList: [],
  page: 1, // 当前页码
  size: 10, // 每页数量
  total: 0, // 总数据量
  status: "", // 需求状态（0：待分配，1：待接单，2：已接单，3：已取消）
});

const loading = ref(false);
const hasMore = ref(true);
let showNodata = ref("blank"); //blank空白nodata无数据list列表 显示空白无数据
onShow(() => {
  uni.$once("refreshList", () => {
    page.page = 1;
    page.orderList = [];
    getPendingList();
  });
  // page.orderList = [];
  // getPendingList();
  eventBus.$on("haveNewMsg", () => {
    page.page = 1;
    page.orderList = [];
    getPendingList();
  });
});

onHide(() => {
  eventBus.$off("haveNewMsg");
});
onUnload(() => {
  eventBus.$off("haveNewMsg");
});
onLoad((options) => {
  if (options && options.status != null) {
    page.status = options.status;
    if (options.status == "") {
      currentIndex.value = 0;
    } else {
      currentIndex.value = Number(options.status) + 1;
    }
  }
  page.page = 1;
  page.orderList = [];
  getPendingList();
});
function tabClick(item) {
  currentIndex.value = item.id;
  page.page = 1;
  page.orderList = [];
  console.log("tabClick", item);
  page.status = item.value;
  showNodata.value = "blank";
  getPendingList();
}
const noData = ref(false);
const loadStatus = ref("loading");
function getPendingList() {
  if (loading.value) return;
  loading.value = true;
  let postObj = {
    page: page.page,
    size: page.size,
    status: page.status,
  };
  http
    .post(proxy.ipConfig.baseUrl2 + "/grzxSh/getXqList", postObj, {
      method: "GET",
    })
    .then((res) => {
      loading.value = false;
      if (res.data.code == 2000) {
        let data = res.data.data;
        page.total = data.total;
        if (page.page === 1) {
          page.orderList = data.records;
        } else {
          page.orderList = [...page.orderList, ...data.records];
        }
        if (page.orderList.length > 0) {
          showNodata.value = "list";
        } else {
          showNodata.value = "nodata";
        }
        hasMore.value = page.orderList.length < page.total;
      } else {
        uni.showToast({
          title: res.data.message,
          icon: "none",
        });
      }
    })
    .catch(() => {
      loading.value = false;
    });
}

function getStatusName(caseStr) {
  // 需求状态（0：待分配，1：待接单，2：已接单，3：已取消）
  switch (caseStr) {
    case 0:
      return "待分配";
    case 1:
      return "待接单";
    case 2:
      return "已接单";
    case 3:
      return "已取消";
    default:
      return "";
  }
}

function getSyTime(time) {
  let date = time.split(" ")[0];
  return date.split("-").join(".");
}

// 议价
function gotoBargain(item) {
  readMsg(item.demandId);
  uni.navigateTo({
    url: `/pagesPackages6/demandShView/bargain?demandId=${item.demandId}&dpId=${item.dpId}`,
  });
  return;
  // uni.navigateTo({ url: "/pagesPackages6/demandShView/bargain" });
  // #ifdef MP-WEIXIN
  uni.navigateTo({
    url: `/pagesPackages6/demandShView/bargainWx?demandId=${item.demandId}&dpId=${item.dpId}`,
  });
  // #endif
  // #ifndef MP-WEIXIN
  uni.navigateTo({
    url: `/pagesPackages6/demandShView/bargainWeb?demandId=${item.demandId}&dpId=${item.dpId}`,
  });
  // #endif
}
// 详情
function gotoDemandDetail(item) {
  if (item.status !== 3) {
    readMsg(item.demandId);
  }
  uni.navigateTo({
    url: "/pagesPackages6/demandShView/detail?demandId=" + item.demandId,
  });
}
// 取消需求
function cancelXq(item) {
  uni.showModal({
    title: "提示",
    confirmColor: "#068324", // 确认按钮颜色
    content: "是否取消需求",
    success: (res) => {
      if (res.confirm) {
        let postObj = {
          demandId: item.demandId,
          reason: "商户已取消需求",
        };
        http
          .post(proxy.ipConfig.baseUrl2 + "/grzxSh/qxXq", postObj, {
            method: "POST",
          })
          .then((res) => {
            if (res.data.code == 2000) {
              uni.showToast({
                title: res.data.message,
                icon: "none",
              });
              page.page = 1;
              page.orderList = [];
              getPendingList();
            }
          });
      }
    },
  });
}
//删除需求
function delSq(item) {
  uni.showModal({
    title: "提示",
    confirmColor: "#068324", // 确认按钮颜色
    content: "是否删除需求",
    success: (res) => {
      if (res.confirm) {
        let postObj = {
          demandId: item.demandId,
          reason: "",
        };
        http
          .post(proxy.ipConfig.baseUrl2 + "/grzxSh/scXq", postObj, {
            method: "POST",
          })
          .then((res) => {
            if (res.data.code == 2000) {
              uni.showToast({
                title: res.data.message,
                icon: "none",
              });
              page.page = 1;
              page.orderList = [];
              getPendingList();
            }
          });
      }
    },
  });
}
// 查看订单
function gotoDdxq(item) {
  uni.navigateTo({
    url: "/pagesPackages3/shfishOrderView/detail?orderId=" + item.orderId,
  });
}
//同意新价格
let showAgreePopup = ref(false);
let agreeType = ref(1);
let agreePrice = ref("");
let dpId = ref("");
let demandId = ref("");
// 同意新价格
function agreeNewPrice(type, item) {
  agreeType.value = type;
  agreePrice.value = item.cpPrice;
  showAgreePopup.value = true;
  dpId.value = item.dpId;
  demandId.value = item.demandId;
}
function cancelClick() {
  showAgreePopup.value = false;
}
function confirmClick() {
  showAgreePopup.value = false;
  readMsg(demandId.value);
  let postObj = {
    dpId: dpId.value,
    demandId: demandId.value,
    token: uni.getStorageSync("user")?.token,
    senderType: uni.getStorageSync("user")?.yhlx,
    status: 1,
    type: 3,
    price: agreePrice.value,
    msg: "",
  };
  http
    .post(proxy.ipConfig.baseUrl2 + "/discussPrice/sendMessage", postObj, {
      method: "POST",
    })
    .then((res) => {
      if (res.data.code == 2000) {
        page.page = 1;
        page.orderList = [];
        getPendingList();
      }
    });
}

function readMsg(xqId) {
  let postObj = {
    xqId,
  };
  http.post(config.baseUrl2 + "/shsy/cleanMsg", postObj, { method: "POST" });
}
const loadMore = (status) => {
  if (!hasMore.value || loading.value) return;
  page.page++;
  getPendingList();
};
</script>

<style lang="scss" scoped>
// .main {
//   height: 100vh;
//   overflow: auto;
// }
.noDataBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666666;
  font-size: 24rpx;

  image {
    width: 400rpx;
    height: 400rpx;
    margin-bottom: 24rpx;
  }
}

.orderList {
  padding: 24rpx;
  background-color: #f4f4f4;
  .orderItem {
    padding: 0 24rpx;
    background-color: #fff;
    margin-bottom: 24rpx;
    border-radius: 20rpx;

    .orderItem_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      font-family: PingFang SC;
      font-size: 24rpx;
      font-weight: normal;

      .title_id {
        color: #666666;
      }

      .title_name {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
        margin: 0 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .title_status {
        height: 50rpx;
        line-height: 50rpx;
        border-radius: 20rpx 0;
        padding: 0 10rpx;
      }

      .status0 {
        color: #ff8d02;
        background: rgba(255, 141, 2, 0.1);
      }

      .status1 {
        color: #267fff;
        background: rgba(38, 127, 255, 0.1);
      }

      .status2 {
        color: #fa5151;
        background: rgba(250, 81, 81, 0.1);
      }

      .status3 {
        color: #666666;
        background: rgba(102, 102, 102, 0.1);
      }
    }

    .orderItem_content {
      padding-bottom: 24rpx;
      // padding: 10rpx 0 14rpx;

      .content_info {
        display: flex;
        border-radius: 20rpx;
        border: 2rpx solid #eaeaea;
        padding: 24rpx;
      }

      .content_item {
        flex: 4;
        font-size: 28rpx;
        color: #333333;
        border-right: 2rpx solid #eaeaea;
        text-align: center;
        padding: 0 15rpx;

        &:first-child {
          padding-left: 0;
        }

        &:last-child {
          border-right: unset;
          // flex: 3;
          padding-right: 0;
        }

        .title {
          color: #666666;
          margin-bottom: 16rpx;
          font-size: 24rpx;
        }

        .price {
          color: #ff0000;
        }
      }

      .content_remind {
        display: flex;
        align-items: center;
        padding: 12rpx 8rpx;
        border-radius: 8rpx;
        gap: 10px;
        background: #f6f6f6;
        font-family: PingFang SC;
        font-size: 24rpx;
        color: #ff0000;

        .remindIcon {
          width: 28rpx;
          height: 28rpx;
        }
      }

      .content_btn {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 24rpx;
        gap: 14rpx;

        .btnItem {
          width: 148rpx;
          border-radius: 10rpx;
          padding: 10rpx 0;
          background: #f4f4f4;
          text-align: center;
          font-family: PingFang SC;
          font-size: 24rpx;
          color: #333333;
        }

        .lightBtn {
          background: #effff3;
          color: #068324;
        }
      }
    }
  }
}
// 同意新价格
.agreeView {
  width: 600rpx;
  padding: 68rpx 50rpx 40rpx;
  box-sizing: border-box;

  .content {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    font-variation-settings: "opsz" auto;
    color: #333333;
    text-align: center;

    .redColor {
      color: #ff0000;
      display: inline;
      font-size: 36rpx;
    }
  }

  .agreeBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 40rpx;
    margin-top: 50rpx;

    button {
      width: 208rpx;
      height: 72rpx;
      line-height: 72rpx;
      border-radius: 72rpx;
      background: #f7f7f7;
      font-family: PingFang SC;
      font-size: 32rpx;
      color: #333333;
      margin: 0;

      &:nth-child(2) {
        background: #068324;
        color: #ffffff;
      }

      &:after {
        border: unset;
      }
    }
  }
}
</style>
