<template>
  <view class="content">
    <!-- 顶部 -->
    <!-- <view
      v-if="!showPopupyyth && !showPopupspth"
      class="navigationBar"
      :style="'background-image:url(' + config.imageUrl + '/ai/bgc.svg)'"
    >
      <view class="navigationBarback" @click="gotoBack()">
        <up-icon name="arrow-left" color="#333333" size="22"></up-icon>
      </view>
      <view class="navigationBarTitleText"> 智能小助手 </view>
      <view class="navigationBarTitleall"> </view>
    </view> -->
    <view class="ai-data">
      {{ data }}
    </view>
    <view class="ai-chat">
      <view class="ai-content">
        <view
          v-for="(items, index) in chatList"
          :key="index"
          class="ai-chat-list"
        >
          <view v-if="items.role == 'ai'" class="ai-chat-item-left">
            <view class="ai-chat-item-left-photo">
              <image
                :src="config.imageUrl + '/ai/ai.svg'"
                class="ai-chat-img"
              ></image>
            </view>
            <view class="ai-chat-item-left-text">
              <view :id="items.id">
                <mp-html :content="items.text" />
              </view>
            </view>
          </view>
          <view v-if="index == 0" class="ai-chat-item-select">
            <view
              @click="addChat(questionChat1)"
              class="ai-chat-item-select-top"
            >
              -你可能感兴趣-
            </view>
            <view
              @click="addChat(questionChat1)"
              class="ai-chat-item-select-text"
            >
              <image
                :src="config.imageUrl + '/ai/chat.svg'"
                class="ai-chat-imgchat"
              ></image>
              {{ questionChat1 }}
            </view>
            <view
              @click="addChat(questionChat2)"
              class="ai-chat-item-select-text"
            >
              <image
                :src="config.imageUrl + '/ai/chat.svg'"
                class="ai-chat-imgchat"
              ></image>
              {{ questionChat2 }}
            </view>
            <view
              @click="addChat(questionChat3)"
              class="ai-chat-item-select-text"
            >
              <image
                :src="config.imageUrl + '/ai/chat.svg'"
                class="ai-chat-imgchat"
              ></image>
              {{ questionChat3 }}
            </view>
          </view>
          <view v-if="items.role == 'user'" class="ai-chat-item-right">
            <view class="ai-chat-item-right-text">
              <text :id="items.id">
                {{ items.text }}
              </text>
            </view>
            <view class="ai-chat-item-right-photo">
              <image
                :src="config.imageUrl + '/ai/user.svg'"
                class="ai-chat-img"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="ai-input">
      <view class="ai-answer">
        小助手累计解决 <text class="answerTetx">{{ totalNum }}</text
        >个问题,今日解决<text class="answerTetx">{{ todayNum }}</text
        >个问题
      </view>
      <view class="ai-input-content">
        <up-input
          @keydown.enter.prevent="send()"
          placeholder="请输入..."
          v-model="searchText"
        >
          <!-- 头部插槽 -->
          <template #prefix>
            <!-- <up-image :src="config.imageUrl + '/ai/mai.svg'" width="36rpx" height="36rpx"></up-image> -->
          </template>

          <!-- 尾部插槽 -->
          <template #suffix>
            <!-- <up-image style="margin-right: 10rpx;" :src="config.imageUrl + '/ai/add.svg'" width="36rpx"
              height="36rpx"></up-image> -->
          </template>
        </up-input>
      </view>
      <view class="ai-input-btn" @click="send()">
        <image
          :src="config.imageUrl + '/ai/send.svg'"
          class="ai-input-btn-image"
        ></image>
      </view>
    </view>
    <!-- 联系专家 -->
    <AssociatedExperts class="contactExpertBox"></AssociatedExperts>
  </view>
  <up-toast ref="uToastRef"></up-toast>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed, nextTick } from "vue";
import Typed from "typed.js";
import { onLoad } from "@dcloudio/uni-app";
import mpHtml from "mp-html/dist/uni-app/components/mp-html/mp-html";
// 导入ipConfig
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
const uToastRef = ref(null);
import AssociatedExperts from "./components/associatedExperts.vue";
// 联系专家
let showPopup = ref(false);

// 导入http
import { http, toast } from "uview-plus";
var page = reactive({
  ipConfig: "unknow",
});
const config = proxy.ipConfig;
function getConfig() {
  page.ipConfig = proxy.ipConfig.baseUrl1;
}
let totalNum = ref(0);
let todayNum = ref(0);
// 当前日期
let data = ref("");
const getData = () => {
  // 创建一个 Date 对象，它会自动初始化为当前日期和时间
  const currentDate = new Date();
  // 获取年份
  const year = currentDate.getFullYear();
  // 获取月份（注意：月份是从 0 开始计数的，所以要加 1）
  const month = currentDate.getMonth() + 1;
  // 获取日期
  const day = currentDate.getDate();
  // 获取小时
  const hours = String(currentDate.getHours()).padStart(2, "0");
  // 获取分钟
  const minutes = String(currentDate.getMinutes()).padStart(2, "0");
  // 获取秒
  const seconds = String(currentDate.getSeconds()).padStart(2, "0");
  // 格式化日期，以 YYYY-MM-DD 格式输出
  data.value = `${year}年${month.toString()}月${day.toString()}日 ${hours}:${minutes}:${seconds}`;
};
// 随机id
const genID = (length = 10) => {
  return (
    Date.now().toString(36) + Math.random().toString(36).substr(2)
  ).substr(0, length);
}; // 如 "lq783h2cp2" [[13,17]]
//智能对话
let chatList = ref([
  {
    role: "ai",
    text: "你好，欢迎使用苏渔智能小助手，关于鳊鱼养殖的专业问题尽管问吧。",
    id: genID(),
  },
  // {
  //   role: 'user',
  //   text: ' 关于发货问题',
  //   id: genID()
  // },
]);
// 日期
setInterval(() => {
  getData();
}, 1000);
//
let searchText = ref("");
// 发送
const send = () => {
  if (searchText.value == "") {
    alert("输入不能为空");
    return;
  }
  let obj = {
    role: "user",
    text: searchText.value,
    id: genID(),
  };
  chatList.value.push(obj);
  searchText.value = "";
  // ... existing code ...
  nextTick(() => {
    const query = uni.createSelectorQuery().in(this);
    query.select(".ai-chat").boundingClientRect();
    query.select(".ai-content").boundingClientRect();
    query.exec((res) => {
      if (res[1]) {
        const chatDom = document.querySelector(".ai-chat");
        if (chatDom) {
          chatDom.scrollTop = chatDom.scrollHeight;
        }
      }
    });
  });
  // ... existing code ...
  //模拟接口返回
  setTimeout(() => {
    chatList.value.push({
      role: "ai",
      text: "问题查询中...",
      id: genID(),
    });
    // 流式输出
    fetch(proxy.ipConfig.baseUrl2 + "/aiChatApi/question", {
      method: "post",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${uni.getStorageSync("user").token}`,
        token: uni.getStorageSync("user").token, // 替换为你的实际token值,
      },
      body: JSON.stringify({
        question: obj.text, // 问题
      }),
    })
      .then(async (response) => {
        // return response.body;
        await getReturnCharts(response.body.getReader(), new TextDecoder());
      })
      .then((body) => {
        // const reader = body.getReader();
        // const decoder = new TextDecoder();
        // return getReturnCharts(reader, decoder, index);
      })
      .catch((error) => {
        console.log("发生错误:", error);
      });
  }, 800);

  ////////////////
};
// ai回答问题答案

// ai回答问题答案
const getReturnCharts = async (reader, decoder) => {
  let buffer = "";
  let aiResponse = chatList.value[chatList.value.length - 1];
  aiResponse.text = "";
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        // 处理最后未解析的数据
        if (buffer) {
          const validData = buffer.replace(/^data:\s*/, "");
          if (validData.trim() !== "[DONE]") {
            // 处理格式
            let formattedData = processFormatting(validData);
            aiResponse.text += formattedData;
          }
        }
        // aiResponse.text += '\n【回答结束】';
        chatList.value = [...chatList.value];
        getAnswerCount();
        break;
      }
      // UTF-8解码处理
      buffer += decoder.decode(value, { stream: true });
      // 处理完整数据块
      const chunks = buffer.split("data:");
      buffer = chunks.pop(); // 保留未完成部分
      for (const chunk of chunks) {
        const trimmedChunk = chunk.trim();
        if (!trimmedChunk || trimmedChunk === "[DONE]") continue;
        try {
          const data = trimmedChunk;
          // 处理格式
          let formattedData = processFormatting(data);
          aiResponse.text += formattedData || "";
          // 实时更新视图
          chatList.value = [...chatList.value];
          // 自动滚动到底部
          nextTick(() => {
            const query = uni.createSelectorQuery().in(this);
            query.select(".ai-chat").boundingClientRect();
            query.select(".ai-content").boundingClientRect();
            query.exec((res) => {
              if (res[1]) {
                const chatDom = document.querySelector(".ai-chat");
                if (chatDom) {
                  chatDom.scrollTop = chatDom.scrollHeight;
                }
              }
            });
          });
        } catch (e) {
          console.error("JSON解析失败:", e);
          aiResponse.text += `[数据解析异常: ${trimmedChunk}]`;
        }
      }
    }
  } catch (error) {
    console.error("流处理异常:", error);
    aiResponse.text += "\n【数据接收异常，请重试】";
    chatList.value = [...chatList.value];
  }
};

// 新增格式处理函数
const processFormatting = (text) => {
  // 处理 ---### 进行换行
  text = text.replace(/---/g, "<br>");
  // 去掉 ###
  text = text.replace(/###/g, "");
  // 处理 ** 加粗，第二个 ** 结束加粗
  text = text.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
  return text;
};

///////////////////
// 添加问题
let questionChat1 = ref(
  "苏渔你好，我是江苏常州养殖户，近期养殖鳊鱼有哪些种苗场可以进货？有什么新品种推荐？需要注意哪些？"
);
let questionChat2 = ref(
  "我想从正规渠道采购投入品，并参考《水产养殖质量安全管理规定》进行科学管理，请给我完整的投入品方案。"
);
let questionChat3 = ref(
  "今天室外最高温度超过40度，我在水质调控、增氧管理、饲料投喂、疾病预防、混养搭配、遮阳设施等方面需要注意哪些？"
);
const addChat = (text) => {
  let obj = {
    role: "user",
    text: text,
    id: genID(),
  };
  chatList.value.push(obj);
  nextTick(() => {
    const query = uni.createSelectorQuery().in(this);
    query.select(".ai-chat").boundingClientRect();
    query.select(".ai-content").boundingClientRect();
    query.exec((res) => {
      if (res[1]) {
        const chatDom = document.querySelector(".ai-chat");
        if (chatDom) {
          chatDom.scrollTop = chatDom.scrollHeight;
        }
      }
    });
  });
  //模拟接口返回
  setTimeout(() => {
    chatList.value.push({
      role: "ai",
      text: "问题查询中...",
      id: genID(),
    });
    // 流式输出
    fetch(proxy.ipConfig.baseUrl2 + "/aiChatApi/question", {
      method: "post",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${uni.getStorageSync("user").token}`,
        token: uni.getStorageSync("user").token, // 替换为你的实际token值,
      },
      body: JSON.stringify({
        question: obj.text, // 问题
      }),
    })
      .then(async (response) => {
        // return response.body;
        await getReturnCharts(response.body.getReader(), new TextDecoder());
      })
      .then((body) => {})
      .catch((error) => {
        console.log("发生错误:", error);
      });
  }, 800);
};
// 返回
const gotoBack = () => {
  uni.redirectTo({
    url: "/pages/indexView/index",
  });
};
//获取回答总数
const getAnswerCount = () => {
  http
    .post(
      proxy.ipConfig.baseUrl2 + "/aiChatApi/numDetail",
      {},
      {
        method: "GET",
      }
    )
    .then((res) => {
      if (res.data.code == 2000) {
        let data = res.data.data;
        totalNum.value = data.totalNum;
        todayNum.value = data.todayNum;
      } else {
        uToastRef.value.show({ type: "error", message: res.data.message });
      }
    });
};

onMounted(() => {
  getConfig();
  getAnswerCount();
  // // #ifdef H5
  return;
  const url = window.location.href;
  const queryString = url.split("?")[1];
  if (queryString && queryString != undefined) {
    const userParamStr = queryString.split("=")[1]; // 提取user参数的值部分
    const decodedUserParamStr = decodeURIComponent(userParamStr);
    if (decodedUserParamStr) {
      uni.setStorageSync("user", JSON.parse(decodedUserParamStr));
    }
  } else {
    uni.removeStorageSync("user"); // 读取后立即清理
  }

  // // #endif
});

onLoad((options) => {
  const script = document.createElement("script");
  script.src = "https://res.wx.qq.com/open/js/jweixin-1.6.0.js";
  script.onload = () => {};
  document.head.appendChild(script);
  // // #ifdef MP-WEIXIN
  if (options && options.user) {
    uni.setStorageSync("user", JSON.parse(options.user));
  } else {
    uni.removeStorageSync("user"); // 读取后立即清理
  }
  // // #endif
});
</script>

<style scoped>
.content {
  width: 100%;
  height: 100vh;
  position: relative;
  background-color: #f3f3f3;
  overflow: hidden;
}

.navigationBar {
  width: 100%;
  height: 176rpx;

  display: flex;
  justify-content: space-between;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  /* background-image: url(/images/ai/bgc.svg); */
  background-size: cover;
  background-repeat: no-repeat;
  padding-top: 88rpx;
  box-sizing: border-box;
}

.navigationBarback {
  height: 88rpx;
  line-height: 88rpx;
  opacity: 1;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  display: flex;
  align-items: center;
  padding-left: 24rpx;
  position: relative;
  z-index: 99;
}

.navigationBarTitleText {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  opacity: 1;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 11;
}

.ai-data {
  width: 100%;
  height: 87.21rpx;
  line-height: 87.21rpx;
  font-size: 24.42rpx;
  text-align: center;
  color: #666666;
  /* margin-top: 176rpx; */
  margin-top: 56rpx;
}

/* 底部 */
.ai-input {
  width: 100%;
  min-height: 166rpx;
  position: fixed;
  left: 0;
  bottom: 0rpx;
  box-sizing: border-box;
  padding: 0 20rpx 88rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
}

.ai-answer {
  width: 100%;
  height: 60rpx;
  line-height: 60rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  /* 小助手累计解决 */
  color: #333333;
}

.answerTetx {
  color: #068324;
}

.ai-input-content {
  width: 616rpx;
  height: 69.77rpx;
  border-radius: 20rpx;
  opacity: 1;
  background: #ededed;
  overflow: hidden;
}

.ai-input-btn {
  width: 73.26rpx;
  height: 69.77rpx;
  background: #068324;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  border-radius: 20rpx;
}

.u-border {
  border: none !important;
}

.ai-input-btn-image {
  width: 36rpx;
  height: 36rpx;
  color: #fff;
}

/* --------- */
.ai-chat {
  box-sizing: border-box;
  width: 100%;
  height: 71vh;
  padding: 0 24rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  display: flex;
  /* 新增 */
  flex-direction: column;
  /* 保证子元素垂直排列 */
}

.ai-content {
  min-height: 100%;
  /* 保证内容高度足够 */
}

.ai-chat-list {
  width: 100%;
}

.ai-chat-item-left {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.ai-chat-item-left-photo {
  width: 60rpx;
  height: 60rpx;
}

.ai-chat-img {
  width: 100%;
  height: 100%;
}

.ai-chat-item-left-text {
  min-width: 200rpx;
  max-width: 504rpx;
  min-height: 50rpx;
  line-height: 50rpx;
  background: #dbfdd9;
  border-radius: 0 20rpx 20rpx 20rpx;
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
}

.ai-chat-item-select {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 32rpx;
}

.ai-chat-item-select-top {
  width: 100%;
  min-height: 50rpx;
  line-height: 50rpx;
  margin-bottom: 32rpx;
  color: #4eac49;
  text-align: center;
  font-size: 28rpx;
  color: #666666;
}

.ai-chat-item-select-text {
  min-width: 200rpx;
  max-width: 562rpx;
  min-height: 50rpx;
  line-height: 50rpx;
  background: #fff;
  border-radius: 0 20rpx 20rpx 20rpx;
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  margin-bottom: 32rpx;
  color: #4eac49;
}

.ai-chat-imgchat {
  width: 22rpx;
  height: 22rpx;
}

.ai-chat-item-right {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.ai-chat-item-right-photo {
  width: 60rpx;
  height: 60rpx;
}

.ai-chat-item-right-text {
  max-width: 488rpx;
  /* min-height: 50rpx; */
  line-height: 50rpx;
  background: #dbfdd9;
  border-radius: 20rpx 0 20rpx 20rpx;
  margin-right: 20rpx;
  padding: 10rpx 20rpx;
}

.ai-chat-item-left-text {
  white-space: pre-wrap;
}
</style>
